import request from '../utils/request'

// 获取视频信息
export function getCameraList(params) {
  return request({
    // url: `/taiyuan/camera/list?pageNum=${params.pageNum}&pageSize=${params.pageSize}`,
    url: `/taiyuan/camera/list?pageSize=${params.pageSize}`,
    method: 'get'
  })
}

export function getEventCount() {
  return request({
    url: '/taiyuan/sjljfscs/stat',
    method: 'get'
  })
}
export function getPipeline() {
  return request({
    url: '/taiyuan/gwyj/stat',
    method: 'get'
  })
}

export function getScene() {
  return request({
    url: '/taiyuan/cjfl/stat',
    method: 'get'
  })
}

export function getDevice() {
  return request({
    url: '/taiyuan/sbxx/stat',
    method: 'get'
  })
}

export function getGoods() {
  return request({
    url: '/taiyuan/yjwz/stat',
    method: 'get'
  })
}

export function getflood() {
  return request({
    url: '/taiyuan/fxnl/stat',
    method: 'get'
  })
}
// 预警
export function getWarning() {
  return request({
    url: '/taiyuan/yjqkfb/stat',
    method: 'get'
  })
}
// 一级预警
export function getFirstLevelWarning() {
  return request({
    url: '/taiyuan/bjdj1/stat',
    method: 'get'
  })
}
// 二级预警
export function getSecondLevelWarning() {
  return request({
    url: '/taiyuan/bjdj2/stat',
    method: 'get'
  })
}
// 三级预警
export function getThreeLevelWarning() {
  return request({
    url: '/taiyuan/bjdj3/stat',
    method: 'get'
  })
}
// 四级预警
export function getFourLevelWarning() {
  return request({
    url: '/taiyuan/bjdj4/stat',
    method: 'get'
  })
}
export function getHistory() {
  return request({
    url: '/taiyuan/lsty/stat',
    method: 'get'
  })
}
export function getSentiment(params) {
  // If params is provided, use them for pagination and filtering
  if (params) {
    let url = `/taiyuan/yqxx/stat?pageNum=${params.pageNum}&pageSize=${params.pageSize}`

    // Add optional filters if provided
    if (params.status) {
      url += `&status=${params.status}`
    }

    if (params.date) {
      url += `&date=${params.date}`
    }

    return request({
      url,
      method: 'get'
    })
  }

  // Default behavior without params (for the home page display)
  return request({
    url: '/taiyuan/yqxx/stat',
    method: 'get'
  })
}
export function getEquipment(id) {
  return request({
    url: '/taiyuan/sbbj/stat',
    method: 'get'
  })
}

export function getDispose() {
  return request({
    url: '/taiyuan/caseInfo/stat',
    method: 'get'
  })
}

export function getRegion() {
  return request({
    url: '/taiyuan/qygl/stat',
    method: 'get'
  })
}

export function getLocation(params) {
  return request({
    url: `/taiyuan/yldsbxx/list?pageSize=${params.pageSize}&sstype=${params.sstype}`,
    method: 'get'
  })
}

export function getEmergencySupplies() {
  return request({
    url: '/taiyuan/zhzs-yjwz/stat',
    method: 'get'
  })
}

export function getPumpStation1() {
  return request({
    url: '/taiyuan/zhzs-bzyxzttj/stat',
    method: 'get'
  })
}

export function getPumpStation2() {
  return request({
    url: '/taiyuan/zhzs-bzyxsjtj/stat',
    method: 'get'
  })
}
export function getPumpStation3() {
  return request({
    url: '/taiyuan/zhzs-bzqcswtj/stat',
    method: 'get'
  })
}
export function getPumpStation4() {
  return request({
    url: '/taiyuan/zhzs-bzcpltj/stat',
    method: 'get'
  })
}
export function getPumpStation5() {
  return request({
    url: '/taiyuan/zhzs-yldtj/stat',
    method: 'get'
  })
}
export function getPumpStation6() {
  return request({
    url: '/taiyuan/zhzs-yjxxyzcdfj/stat',
    method: 'get'
  })
}

export function getImpactofwaterlogging() {
  return request({
    url: '/taiyuan/zhzs-nlyxfx/stat?name=40',
    method: 'get'
  })
}

export function getImpactofwaterlogging1() {
  return request({
    url: '/taiyuan/zhzs-nlybxx/stat?name=40',
    method: 'get'
  })
}

export function getImpactofwaterlogging2() {
  return request({
    url: '/taiyuan/zhzs-nlyb/stat',
    method: 'get'
  })
}

export function getDeviceDetails(sbcode) {
  return request({
    url: `/taiyuan/yldsbxx/list?sbcode=${sbcode}`,
    method: 'get'
  })
}
export function getDeviceDetails1(id) {
  return request({
    url: `/taiyuan/yldsbxx/${id}`,
    method: 'get'
  })
}

export function getLocation2(params) {
  return request({
    url: `/taiyuan/yldsbxx/list?pageSize=${params.pageSize}&category=${params.category}`,
    method: 'get'
  })
}

export function getMore(params) {
  return request({
    url: params
      ? `/taiyuan/caseInformation/list?eventGridName=${params?.eventGridName}&rptTime=${params?.rptTime}`
      : `/taiyuan/caseInformation/list`,
    method: 'get'
  })
}

export function getLocation3(pageNum = 1, pageSize = 20, cphm = '', jsrxm = '') {
  return request({
    url: `/taiyuan/fkclxxb/list?pageNum=${pageNum}&pageSize=${pageSize}&cphm=${cphm}&jsrxm=${jsrxm}`,
    method: 'get'
  })
}
export function getCarData(id) {
  return request({
    url: `/taiyuan/fkclxxb/list?sbbh=${id}`,
    method: 'get'
  })
}

export function getWarehouse(type) {
  return request({
    url: `/taiyuan/yjcbd/list?type=${type}`,
    method: 'get'
  })
}

export function getSupplies(params) {
  let url = `/taiyuan/wz/list?pageNum=${params.pageNum}&pageSize=${params.pageSize}`

  if (params) {
    const queryParams = []
    if (params.cbdId) {
      queryParams.push(`cbdId=${params.cbdId}`)
    }
    if (params.wzlx) {
      queryParams.push(`wzlx=${params.wzlx}`)
    }
    if (queryParams.length > 0) {
      url += '&' + queryParams.join('&')
    }
  }

  return request({
    url,
    method: 'get'
  })
}

export function getSuppliesDetails(id) {
  return request({
    url: `taiyuan/wz/list?pageNum=1&pageSize=1&id=${id}`,
    method: 'get'
  })
}

export function getSentimentTable(params) {
  return request({
    url: `/taiyuan/yqxxb/list?pageNum=${params.pageNum}&pageSize=${params.pageSize}`,
    method: 'get'
  })
}

export function getSceneScene(type) {
  return request({
    url: `/taiyuan/yldsbxx/sbxxlist?sstype=${type}`,
    method: 'get'
  })
}

export function getdeviceInfo(yldid = 'YLD-723') {
  return request({
    url: `/taiyuan/yldsbxx/list?yldId=${yldid}`,
    method: 'get'
  })
}
export function getwaterLevel(sncode = '112100021087', pageSize = 10) {
  return request({
    url: `/taiyuan/yldsbxx/sbswsj?pageSize=${pageSize}&sncode=${sncode}`,
    method: 'get'
  })
}

export function getQzxx(yldid = 'YLD-277') {
  return request({
    url: `/taiyuan/yldqzxx/list?yldId=${yldid}`,
    method: 'get'
  })
}
export function getVideoInfo(yldid = 'YLD-54') {
  return request({
    url: `/taiyuan/yldsxt/list?yldId=${yldid}`,
    method: 'get'
  })
}

export function getWzData() {
  return request({
    url: '/taiyuan/wz/list?id=1d2e7f0b-688f-4drg4-80d7-789532',
    method: 'get'
  })
}
export function getyldfq(yldName) {
  return request({
    url: `/taiyuan/yldpsfq/list?yldName=${yldName}`,
    method: 'get'
  })
}

// 重点场景-地图打点
export function getZdcjType() {
  return request({
    url: '/taiyuan/yldsbxx/yldTypeList',
    method: 'get'
  })
}

export function getRydw() {
  return request({
    url: '/taiyuan/yjdw/status',
    method: 'get'
  })
}
export function getZdcj(type) {
  return request({
    url: `/taiyuan/yldsbxx/yldList/${type}`,
    method: 'get'
  })
}

export function getYcyj(garde) {
  return request({
    url: `/taiyuan/statistics/list2/${garde}`,
    method: 'get'
  })
}

export function getRydwMap(status) {
  return request({
    url: `/taiyuan/yjdw/yjdw/${status}`,
    method: 'get'
  })
}


export function getFxryData(id) {
  return request({
    url: `/taiyuan/yjdw/${id}`,
    method: 'get'
  })
}

export function getFxbc() {
  return request({
    url: '/taiyuan/fkclxxb/status',
    method: 'get'
  })
}

export function getFxbcMap(status) {
  return request({
    url: `/taiyuan/fkclxxb/fkcl/${status}`,
    method: 'get'
  })
}

export function getCkMap() {
  return request({
    url: '/taiyuan/yjcbd/list?pageSize=100',
    method: 'get'
  })
}
export function getCkDataList(id) {
  return request({
    url: `/taiyuan/yjcbd/${id}`,
    method: 'get'
  })
}

export function getVideo(params) {
   const queryParams = new URLSearchParams()
    // 添加其他查询参数
      if (params.pageSize) {
    queryParams.append('pageSize', params.pageSize)
  }

  if (params.yycjmc) {
    queryParams.append('yycjmc', params.yycjmc)
  }
  if (params.sxtmc) {
    queryParams.append('sxtmc', params.sxtmc)
  }
  const url = queryParams.toString() ? `/taiyuan/sxtxxb/list?${queryParams.toString()}` : '/taiyuan/sxtxxb/list'

  return request({
    url: url,
    method: 'get'
  })
}

export function getVideoType() {
  return request({
    url: '/taiyuan/sxtxxb/type',
    method: 'get'
  })
}

export function getVideoDeatils(id) {
  return request({
    url: `/taiyuan/sxtxxb/${id}`,
    method: 'get'
  })
}

// 防汛简报相关接口
export function getFloodReport(params = {}) {
 const queryParams = new URLSearchParams()
    // 添加其他查询参数
      if (params.pageSize) {
    queryParams.append('pageSize', params.pageSize)
  }
      if (params.pageNum) {
    queryParams.append('pageNum', params.pageNum)
  }
  if (params.type) {
    queryParams.append('type', params.type)
  }
  const url = queryParams.toString() ? `/taiyuan/fxjb/list?${queryParams.toString()}` : '/taiyuan/fxjb/list'

  return request({
    url: url,
    method: 'get'
  })
}


export function statisticsToday() {
  return request({
    url: `/taiyuan/statistics/today`,
    method: 'get'
  })
}
