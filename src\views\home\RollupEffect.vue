<template>
  <div class="app-container">
    <div class="video-container" id="video-container" @mousemove="handleMouseMove">
      <div id="player1" :style="{ width: '100%', height: '100%' }"></div>
      <div
        id="player2"
        :style="{
          width: '100%',
          height: '100%',
          clipPath: clipPath
        }"
      ></div>
      <!-- 黄色分隔线 -->
      <div v-if="showLine" id="line" :style="{ left: linePosition + 'px' }"></div>

      <!-- 连接状态提示 -->
      <div v-if="!apiOnReady" class="connection-status">
        <div class="loading-spinner"></div>
        <div class="status-text">正在连接数字孪生服务器...</div>
        <div class="server-info">服务器: {{ host }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'

onBeforeUnmount(() => {
  console.log('RollupEffect组件卸载，清理资源')
  try {
    if (api1.value && typeof api1.value.destroy === 'function') {
      api1.value.destroy()
    }
    if (api2.value && typeof api2.value.destroy === 'function') {
      api2.value.destroy()
    }
  } catch (error) {
    console.error('清理播放器资源时出错:', error)
  }
})
// 响应式状态管理
const isInRollMode = ref(true) // 一进入页面就启用卷帘模式
const clipPath = ref('inset(0 0 0 50%)') // 初始显示50%分割
const showLine = ref(true) // 初始显示分隔线
const linePosition = ref(0) // 初始分隔线位置
const containerWidth = ref(0) // 容器宽度
const containerHeight = ref(0) // 容器高度
const api1 = ref(null)
const api2 = ref(null)
const apiOnReady = ref(false)

// 云渲染服务器地址
const host = import.meta.env.VITE_DTS_URL

// 处理鼠标移动事件，更新卷帘效果
const handleMouseMove = event => {
  if (!isInRollMode.value || containerWidth.value === 0) return

  // 获取容器位置并计算相对坐标
  const container = document.getElementById('video-container')
  const rect = container.getBoundingClientRect()
  const x = event.clientX - rect.left

  // 限制x在容器范围内
  const limitedX = Math.max(0, Math.min(x, containerWidth.value))

  // 更新分隔线位置
  linePosition.value = limitedX
  showLine.value = true

  // 计算裁剪百分比并应用
  const percentage = (100 * limitedX) / containerWidth.value
  clipPath.value = `inset(0 0 0 ${percentage}%)`
}

// 播放器1初始化完成回调
const onReady1 = () => {
  console.log('播放器1初始化完成')
  apiOnReady.value = true
  if (api1.value) {
    try {
      api1.value.settings.setMainUIVisibility(false)
      api1.value.settings.setCampassVisible(false)
      // 初始化完成后设置相机位置
      nextTick(() => {
        setupRollMode()
      })
    } catch (error) {
      console.error('播放器1设置失败:', error)
    }
  }
}

// 播放器2初始化完成回调
const onReady2 = () => {
  console.log('播放器2初始化完成')
  apiOnReady.value = true
  if (api2.value) {
    try {
      api2.value.settings.setMainUIVisibility(false)
      api2.value.settings.setCampassVisible(false)
      // 初始化完成后设置相机位置
      nextTick(() => {
        setupRollMode()
      })
    } catch (error) {
      console.error('播放器2设置失败:', error)
    }
  }
}

// 设置卷帘模式的相机和交互状态
const setupRollMode = () => {
  if (api1.value && api2.value) {
    // 禁用交互
    api1.value.settings.setEnableInteract(false)
    api2.value.settings.setEnableInteract(false)
    // 设置相机位置
    api1.value.camera.set(490256.214375, 2495216.6375, 188.812012, -31.755644, 52.429024, 0)
    api2.value.camera.set(490256.214375, 2495216.6375, 88.812012, -31.755644, 52.429024, 0)

    // 激活视频窗口
    const player2 = document.getElementById('player2')
    if (player2) {
      player2.focus()
      player2.click()
    }
  }
}

// 事件处理函数
const onEvent = event => {
  if (event.eventtype === 'LeftMouseButtonClick' && event.Type === 'TileLayer') {
    console.info('当前点击图层位置：' + event.MouseClickPoint)
  }

  if (event.eventtype === 'LeftMouseButtonClick' && event.Type === 'marker') {
    // 可添加标记点点击处理逻辑
  }
}

// 日志输出函数
const onLog = (s, nnl) => {
  const logStr = s + (nnl ? '' : '\n')
  console.info(logStr)
}

// 组件挂载后初始化播放器和监听容器尺寸变化
onMounted(() => {
  console.log('RollupEffect组件挂载，开始初始化')
  console.log('使用的服务器地址:', host)

  // 监听容器尺寸变化
  const container = document.getElementById('video-container')
  if (container) {
    // 初始化容器尺寸
    const updateContainerSize = () => {
      containerWidth.value = container.offsetWidth
      containerHeight.value = container.offsetHeight
      // 确保分隔线在容器中间
      linePosition.value = containerWidth.value / 2
      clipPath.value = `inset(0 0 0 50%)`
    }

    // 初始更新
    updateContainerSize()

    // 监听窗口大小变化
    window.addEventListener('resize', updateContainerSize)

    // 组件卸载时清理
    const cleanup = () => {
      window.removeEventListener('resize', updateContainerSize)
    }

    // 在组件卸载时执行清理
    onUnmounted(cleanup)
  }

  if (window.DigitalTwinPlayer) {
    try {
      console.log('开始初始化数字孪生播放器')

      // 初始化第一个播放器
      const options1 = {
        domId: 'player1',
        id: '2551604637832',
        ui: {
          startupInfo: false,
          statusIndicator: false,
          statusButton: false,
          fullscreenButton: false,
          homeButton: false,
          mainUI: false,
          campass: false
        },
        apiOptions: {
          onReady: onReady1,
          onLog: onLog,
          onEvent: onEvent
        }
      }

      console.log('创建播放器1，配置:', options1)
      api1.value = new window.DigitalTwinPlayer(host, options1).getAPI()

      // 初始化第二个播放器
      const options2 = {
        domId: 'player2',
        id: '2551663703769',
        ui: {
          startupInfo: false,
          statusIndicator: false,
          statusButton: false,
          fullscreenButton: false,
          homeButton: false,
          mainUI: false,
          campass: false
        },
        apiOptions: {
          onReady: onReady2,
          onLog: onLog,
          onEvent: onEvent
        }
      }

      console.log('创建播放器2，配置:', options2)
      api2.value = new window.DigitalTwinPlayer(host, options2).getAPI()

    } catch (error) {
      console.error('初始化数字孪生播放器失败:', error)
      console.error('错误详情:', error.message)
      console.error('请检查服务器连接:', host)
    }
  } else {
    console.error('DigitalTwinPlayer 未加载，请确保已引入相关库')
    console.error('请检查 ac.min.js 是否正确加载')
  }
})

// 组件卸载时的清理函数
const onUnmounted = callback => {
  // 模拟Vue的onUnmounted生命周期
  // 在实际使用中，应使用Vue提供的onUnmounted
  window.addEventListener('beforeunload', callback)
  return () => window.removeEventListener('beforeunload', callback)
}
</script>

<style scoped>
/* 确保父元素充满整个视口 */
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 防止页面滚动 */
}

.app-container {
  width: 100%;
  height: 100%;
  padding: 0;
  pointer-events: auto;
}

#player1 {
  position: absolute;
  top: 0px;
  left: 0px;
}

#player2 {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 10;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.streamingVideoCursorPointer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#line {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: yellow;
  z-index: 100;
  pointer-events: none;
}

/* 连接状态提示样式 */
.connection-status {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  z-index: 200;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.server-info {
  font-size: 12px;
  color: #ccc;
}
</style>
