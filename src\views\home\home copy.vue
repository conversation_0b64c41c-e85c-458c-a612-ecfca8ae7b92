<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>

    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <!-- <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" /> -->
            <!-- <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" /> -->
            <!-- <span class="temperature">17℃</span> -->
          </div>
        </div>
        <div class="content-layout-first">
          <div class="content-layout-header">
            <div class="weather-info-bar">
              <div class="current-weather">
                <span class="weather-label">当前天气</span>
                <div class="city-info">
                  <img class="location-icon" src="@/assets/images/home/<USER>" alt="" />
                  <span class="city-name"
                    >{{ weaherData.city }} &nbsp;&nbsp;{{ weaherData.minTemperature }}~{{
                      weaherData.maxTemperature
                    }}</span
                  >
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
              <div class="weather-detail">
                <div class="weather-icon-large">
                  <img src="@/assets/images/home/<USER>" alt="大雨" />
                  <div>{{ weaherData.daytimeWind }}，{{ weaherData.daytimeWeather }}</div>
                </div>
                <div class="weather-icon-large">
                  <img src="@/assets/images/home/<USER>" alt="大雨" />
                  <div>{{ weaherData.nighttimeWind }}，{{ weaherData.nighttimeWeather }}</div>
                </div>
                <!-- <div class="weather-data">
                  <div class="weather-type">
                    <span class="weather-name">大雨</span>
                    <span class="weather-temp">19优</span>
                  </div>
                  <div class="wind-info">
                    <span>东风 </span>
                    <span> 2级</span>
                  </div>
                </div> -->
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
              <div class="weather-forecast">
                <!-- <div class="forecast-item">
                  <div class="forecast-title">24h累计降雨</div>
                  <div class="forecast-value">
                    <span class="forecast-value-num">50</span>
                    <span class="forecast-value-unit">mm</span>
                  </div>
                </div> -->
                <div class="forecast-item">
                  <div class="forecast-title">未来24h天气</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">{{ weaherData.forecast_24h }}</span>
                    <!-- <span class="forecast-value-unit">mm</span> -->
                  </div>
                </div>
                <div class="forecast-item">
                  <div class="forecast-title">检测次数</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">{{ weaherData.monitor_count }}</span>
                    <!-- <span class="forecast-value-unit">mm</span> -->
                  </div>
                </div>
                <div class="forecast-item">
                  <div class="forecast-title">预警等级</div>
                  <div class="forecast-value1">
                    <span class="forecast-value-num">{{ weaherData.monitor_level }}</span>
                    <!-- <span class="forecast-value-unit">mm</span> -->
                  </div>
                </div>
              </div>
              <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />

              <div class="publish-info" @click="yuntuClick">
                <span class="publish-label">云图</span>
                <div class="publish-times">
                  <!-- <div class="time-item">开始时间: 20:50:00</div> -->
                  <!-- <div class="time-item">结束时间: 18:00:00</div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 上下布局容器 -->
        <div class="left-content">
          <!-- 第二部分 -->
          <div class="content-layout-second">
            <div class="second-section-top">
              <!-- 标题区域 -->
              <div class="rainfall-title-area">
                <div class="rainfall-title-left">
                  <span class="rainfall-main-title">降雨日历</span>
                </div>
              </div>
              <!-- 图表区域 -->
              <div class="rainfall-chart-area">
                <div ref="rainfallChartRef" v-chart-resize class="rainfall-chart"></div>
                <div class="rainfall-title-right">
                  <div class="rainfall-stats">
                    <div class="rainfall-stat-item">
                      <span class="rainfall-stat-label">全市平均</span>
                      <span class="rainfall-stat-label">总雨量</span>
                      <span class="rainfall-stat-value">{{ total_rainfall }}mm</span>
                    </div>
                    <div class="rainfall-stat-item">
                      <span class="rainfall-stat-label">当前雨量</span>
                      <span class="rainfall-stat-value current">{{ current_rainfall }}mm</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="second-section-left">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <span class="main-title">区域概览</span>
                </div>
                <!-- <div class="title-right">
                  <span class="update-time">预测/实时/历史</span>
                </div> -->
              </div>
              <!-- 图表区域 -->
              <div class="chart-area">
                <div class="chart-item-container">
                  <div class="venn-diagram">
                    <!-- 三个圆圈 -->
                    <div class="circle circle-1" @click="handleCircleClick(1)">
                      <div class="circle-label circle-label-1">
                        <div class="label-text">报警事件点数{{ sghxData?.alarm_event || '0' }}（个）</div>
                        <!-- <div class="label-unit">(个)</div> -->
                      </div>
                    </div>
                    <div class="circle circle-2" @click="handleCircleClick(2)">
                      <div class="circle-label circle-label-2">
                        <div class="label-text">监测报警点数{{ sghxData?.forecast_point || '0' }}（个）</div>
                      </div>
                    </div>
                    <div class="circle circle-3" @click="handleCircleClick(3)">
                      <div class="circle-label circle-label-3">
                        <div class="label-text">预报内涝点数{{ sghxData?.monitor_point || '0' }}（个）</div>
                        <!-- <div class="label-unit">(个)</div> -->
                      </div>
                    </div>

                    <!-- 交集区域的数字 -->
                    <div class="intersection intersection-12">{{ sghxData?.alarm_forecast || '0' }}</div>
                    <div class="intersection intersection-13">{{ sghxData?.forecast_monitor || '0' }}</div>
                    <div class="intersection intersection-23">{{ sghxData?.alarm_monitor || '0' }}</div>
                    <div class="intersection intersection-123">{{ sghxData?.alarm_forecast_monitor || '0' }}</div>
                  </div>
                </div>
                <div ref="barChartRef" v-chart-resize class="bar-chart"></div>
              </div>
            </div>
            <div class="second-section-footer">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <!-- <span class="main-title">区域数据统计</span> -->
                </div>
              </div>
              <!-- 图表区域 -->
              <div class="chart-area">
                <div ref="footerChartRef" v-chart-resize class="footer-chart"></div>
              </div>
            </div>
          </div>

          <!-- 第三部分 -->
          <div class="content-layout-third">
            <div class="second-section-right">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <span class="main-title">积水点统计</span>
                </div>
              </div>
              <div class="chart-item-container">
                <div class="chart-item chart-item-1">
                  <div class="chart-item-1-1">
                    <div class="chart-item-title">泵站运行统计</div>
                    <div ref="pumpRunningChartRef" v-chart-resize class="pump-running-chart"></div>
                  </div>
                  <div class="chart-item-1-2">
                    <div class="pump-station-stats">
                      <div class="stats-row">
                        <div class="stat-item">
                          <div class="stat-label">泵站数</div>
                          <div class="stat-value">
                            {{ total_pumps?.value || '300' }}<span class="stat-unit">{{ total_pumps?.unit }}</span>
                          </div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-label">排水系统总排能力</div>
                          <div class="stat-value">
                            {{ total_pumping_capacity?.value || '5000'
                            }}<span class="stat-unit">{{ total_pumping_capacity?.unit }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="stats-row">
                        <div class="stat-item">
                          <div class="stat-label">泵运行数</div>
                          <div class="stat-value">
                            {{ running_pumps?.value || '100' }}<span class="stat-unit">{{ running_pumps?.unit }}</span>
                          </div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-label">排水系统运行排量</div>
                          <div class="stat-value">
                            {{ running_pumping_capacity?.value || '4000'
                            }}<span class="stat-unit">{{ running_pumping_capacity?.unit }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 第一个图表 -->
                </div>
                <div class="chart-item chart-item-2">
                  <!-- 第二个图表 -->
                  <div class="chart-item-2-1">
                    <div class="chart-item-title">
                      管网总长及健康度统计{{ pipeline_total_length?.value }}{{ pipeline_total_length?.unit }}
                    </div>
                    <div ref="oilWaterLevelChartRef" v-chart-resize class="oil-water-level-chart"></div>
                  </div>
                  <div class="chart-item-2-2">
                    <div class="reservoir-stats">
                      <div class="stats-grid">
                        <div class="stat-item">
                          <div class="stat-label">缓洪池总规模</div>
                          <div class="stat-value">
                            {{ buffer_pool_total?.value || '3'
                            }}<span class="stat-unit"> {{ buffer_pool_total?.unit || '万m³' }}</span>
                          </div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-label">排洪实总规模</div>
                          <div class="stat-value">
                            {{ drainage_channel_total?.value || '3'
                            }}<span class="stat-unit"> {{ drainage_channel_total?.unit || '万m³' }}</span>
                          </div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-label">缓洪池使用规模</div>
                          <div class="stat-value">
                            {{ buffer_pool_used?.value || '3'
                            }}<span class="stat-unit"> {{ buffer_pool_used?.unit || '万m³' }}</span>
                          </div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-label">排洪实使用规模</div>
                          <div class="stat-value">
                            {{ drainage_channel_used?.value || '3'
                            }}<span class="stat-unit"> {{ drainage_channel_used?.unit || '万m³' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="third-right">
              <!-- 左侧区域 -->
              <div class="third-section-left">
                <!-- 标题区域 -->
                <div class="title-area">
                  <div class="title-left">
                    <span class="main-title">人员物资统计</span>
                    <!-- <span class="title">预警数 {{ totalWarning || '0' }}</span>
                    <span class="title">离线数 {{ totalOffline || '0' }}</span> -->
                  </div>
                  <!-- <div class="title-right">
                    <span class="warning-count">更新时间{{ scenetime || '0' }}</span>
                  </div> -->
                </div>
                <!-- 标签分类展示容器 -->
                <div class="tags-container">
                  <div class="personnel-stats-grid">
                    <!-- 防汛人员 -->
                    <div class="stats-card personnel-card">
                      <div class="card-header">
                        <span class="card-title">防汛人员</span>
                      </div>
                      <div class="card-content">
                        <div class="main-stats">
                          <div class="percentage-display">
                            <span class="percentage">{{ flood_control_personnel?.dispatch_rate || '0.00%' }}</span>
                            <span class="percentage-label">调度率</span>
                          </div>
                          <div class="stats-details">
                            <div class="detail-row">
                              <span class="detail-label">调度人数</span>
                              <span class="detail-value"
                                >{{ flood_control_personnel?.dispatched_number.value || '300' }}
                                {{ flood_control_personnel?.dispatched_number.unit || '人' }}</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">总人数</span>
                              <span class="detail-value"
                                >{{ flood_control_personnel?.total_number.value || '300' }}
                                {{ flood_control_personnel?.total_number.unit || '人' }}</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">平均响应时长</span>
                              <span class="detail-value"
                                >{{ flood_control_personnel?.average_response_time.value || '30' }}分钟</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 应急家具 -->
                    <div class="stats-card supplies-card" @click="bcClick">
                      <div class="card-header">
                        <span class="card-title">应急泵车</span>
                      </div>
                      <div class="card-content">
                        <div class="main-stats">
                          <div class="percentage-display">
                            <span class="percentage">{{ emergency_pump_trucks?.dispatch_rate || '0.00%' }}</span>
                            <span class="percentage-label">调度率</span>
                          </div>
                          <div class="stats-details">
                            <div class="detail-row">
                              <span class="detail-label">调度数量</span>
                              <span class="detail-value"
                                >{{ emergency_pump_trucks?.dispatched_number.value || '300' }}台</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">总数</span>
                              <span class="detail-value"
                                >{{ emergency_pump_trucks?.total_number.value || '300' }}台</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">平均响应时长</span>
                              <span class="detail-value"
                                >{{ emergency_pump_trucks?.average_response_time.value }}分钟</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 防汛车辆 -->
                    <div class="stats-card vehicle-card" @click="getCarLocation(floodvehicle)">
                      <div class="card-header">
                        <span class="card-title">防汛车辆</span>
                      </div>
                      <div class="card-content">
                        <div class="main-stats">
                          <div class="percentage-display">
                            <span class="percentage">{{ flood_control_vehicles?.dispatch_rate || '0.00%' }}</span>
                            <span class="percentage-label">使用率</span>
                          </div>
                          <div class="stats-details">
                            <div class="detail-row">
                              <span class="detail-label">调度数量</span>
                              <span class="detail-value"
                                >{{ flood_control_vehicles?.dispatched_number.value || '300' }}台</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">总数</span>
                              <span class="detail-value"
                                >{{ flood_control_vehicles?.total_number.value || '300' }}台</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">平均响应时长</span>
                              <span class="detail-value"
                                >{{ flood_control_vehicles?.average_response_time.value }}分钟</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 防汛物资 -->
                    <div class="stats-card materials-card" @click="wzClick">
                      <div class="card-header">
                        <span class="card-title">防汛物资</span>
                      </div>
                      <div class="card-content">
                        <div class="main-stats">
                          <div class="percentage-display">
                            <span class="percentage">{{ flood_control_materials?.dispatch_rate || '0.00%' }}</span>
                            <span class="percentage-label">使用率</span>
                          </div>
                          <div class="stats-details">
                            <div class="detail-row">
                              <span class="detail-label">调度数量</span>
                              <span class="detail-value"
                                >{{ flood_control_materials?.dispatched_number.value || '300' }}件</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">总数</span>
                              <span class="detail-value"
                                >{{ flood_control_materials?.total_number.value || '300' }}件</span
                              >
                            </div>
                            <div class="detail-row">
                              <span class="detail-label">平均响应时长</span>
                              <span class="detail-value"
                                >{{ flood_control_materials?.average_response_time.value }}分钟</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 右侧区域 -->
              <div class="third-section-right">
                <!-- 右侧内容区域 -->
                <div class="tags-container">
                  <div class="tags-top">
                    <!-- 标题区域 -->
                    <div class="title-area">
                      <div class="title-left">
                        <span class="main-title">调度指令统计</span>
                        <!--   <span class="title">预警数{{ devictotalWarning || '0' }}</span> -->
                      </div>
                      <!-- <div class="title-right">
                    <span class="warning-count">更新时间{{ devictime || '0' }}</span>
                  </div> -->
                    </div>
                    <div class="dispatch-stats-container">
                      <!-- 左侧：调度总数 -->
                      <div class="dispatch-total-section">
                        <div class="dispatch-label">调度总数</div>
                        <div class="dispatch-count">{{ total_scheduling?.value }}{{ total_scheduling?.unit }}</div>
                      </div>

                      <!-- 右侧：柱状图 -->
                      <div class="dispatch-chart-section">
                        <div ref="dispatchChartRef" v-chart-resize class="dispatch-chart"></div>
                      </div>
                    </div>
                  </div>
                  <div class="tags-footer" style="pointer-events: auto">
                    <!-- 标题区域 -->
                    <div class="title-area" style="pointer-events: auto">
                      <div class="title-left">
                        <span class="main-title">调度指令</span>
                        <!--   <span class="title">预警数{{ devictotalWarning || '0' }}</span> -->
                      </div>
                      <div class="title-right" style="z-index: 10000; position: relative">
                        <span
                          class="warning-count dispatch-more-btn"
                          @click.stop="openDdzlDialog"
                          @mousedown.stop
                          @mouseup.stop
                          style="
                            cursor: pointer;
                            z-index: 10001;
                            position: relative;
                            pointer-events: auto;
                            display: inline-block;
                          "
                          >更多</span
                        >
                      </div>
                    </div>

                    <!-- 表格区域 -->
                    <div class="dispatch-table-container">
                      <table class="dispatch-table">
                        <thead>
                          <tr>
                            <th>推送部门</th>
                            <th>调度时间</th>
                            <th>调度对象</th>
                            <th>调度次数</th>
                            <th>调度方式</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in ddzlDataList" :key="index">
                            <td>{{ item.dispatch_department }}</td>
                            <td>{{ item.dispatch_time }}</td>
                            <td>{{ item.dispatch_object }}</td>
                            <td>{{ item.dispatch_count }}</td>
                            <td>{{ item.dispatch_method }}</td>
                          </tr>
                        </tbody>
                      </table>
                      <!-- 测试按钮 -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section"></div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 上部分：用户信息和返回门户 -->
        <!-- <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>
        </div> -->

        <!-- 下部分：新增的内容区域 -->
        <div class="right-content">
          <!-- 这里可以添加下部分的具体内容 -->
          <div class="right-content-left">
            <!-- 左侧内容 -->
            <!-- 顶部状态信息 -->
            <div class="title-area">
              <div class="title-left">
                <span class="title">视频在线 </span
                ><span class="title-number">({{ onlineVideos }}/{{ totalVideos }})</span>
              </div>
            </div>
            <div class="video-content">
              <!-- 添加tab切换导航 -->
              <div class="video-tabs">
                <div class="tab-item" :class="{ active: activeTab === 'floodPoint' }" @click="switchTab('floodPoint')">
                  易涝点
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'pumpStation' }"
                  @click="switchTab('pumpStation')"
                >
                  泵站
                </div>
                <div
                  class="tab-item"
                  :class="{ active: activeTab === 'comprehensive' }"
                  @click="switchTab('comprehensive')"
                >
                  综治
                </div>
                <div class="tab-item" :class="{ active: activeTab === 'subway' }" @click="switchTab('subway')">
                  地铁
                </div>
              </div>
              <!-- 视频网格容器 -->
              <div class="video-grid">
                <!-- 使用动态数据循环渲染视频窗口 -->
                <div v-for="(video, index) in displayedVideos" :key="video.id" class="video-window">
                  <div class="video-area">
                    <video :id="`video-player-${index}`" controls playsinline></video>
                  </div>
                  <div class="text-content" @click="handleVideoClick(video)">
                    <div class="location">{{ video.location ? video.location : '西中环南延' }}</div>
                    <div class="params">
                      <div class="param-item">
                        <span class="label">水位(cm):</span>
                        <span class="value">{{ video.waterLevel ? video.waterLevel : '0' }}</span>
                      </div>
                      <div class="param-item">
                        <span class="label">雨量(mm/h):</span>
                        <span class="value">{{ video.rainfall ? video.rainfall : '0' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页控件 -->
            <!-- <div class="pagination-container">
              <a-pagination
                v-model:current="currentPage"
                :defaultPageSize="defaultPageSize"
                :total="totalVideos"
                :pageSize="pageSize"
                @change="handlePageChange"
                @showSizeChange="onShowSizeChange"
                :showTotal="total => `共 ${total} 条`"
              />
            </div> -->
          </div>
          <div class="right-content-middle">
            <!-- 中间内容 -->
            <div class="middle-top-section">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <span class="title">易涝点报警</span>
                  <span class="title-desc">报警数{{ yldbjtotalWarning }}</span>
                </div>
                <div class="title-right">
                  <span class="area" @click="openYldbjDialog">
                    <span style="cursor: pointer">更多</span>
                  </span>
                </div>
              </div>
              <!-- 图表区域 -->
              <div class="middle-top-section-table">
                <!-- 顶部统计卡片 -->
                <div class="stats-cards">
                  <div
                    class="stats-card"
                    v-for="(item, index) in yldbjDataList"
                    :key="index"
                    @click="handleTagClick(item)"
                  >
                    <div class="stats-img">
                      <img :src="item.icon" alt="" />
                    </div>
                    <div class="stats-content">
                      <div class="stats-title">{{ item.type || '未知类型' }}</div>
                      <div class="stats-value">
                        <span style="color: white">{{ item.total || '0' }}</span
                        >/<span style="color: orange">{{ item.online || '0' }}</span
                        >/<span style="color: red">{{ item.warning || '0' }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 表格区域 -->
                <div class="warning-table-wrapper">
                  <table class="warning-table">
                    <thead>
                      <tr>
                        <th>所属区域</th>
                        <th>报警时间</th>
                        <th>积水深度 (m)</th>
                        <th>报警等级</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in yldbjtbData" :key="index" @click="handleClickTable(item)">
                        <td>{{ item.area }}</td>
                        <td>{{ item.forecastTime }}</td>
                        <td>{{ item.waterDepth }}</td>
                        <td>{{ item.reportLevel }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="middle-bottom-section">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <span class="title">排水设施报警</span>
                  <span class="title-desc">报警数{{ pssstotalWarning }}</span>
                </div>
                <div class="title-right">
                  <span class="area" @click="openPsssbjDialog">
                    <span style="cursor: pointer">更多</span>
                  </span>
                </div>
              </div>
              <!-- 表格区域 -->
              <div class="middle-bottom-section-table">
                <!-- 顶部统计卡片 -->
                <div class="stats-cards-container">
                  <div class="stats-cards-grid">
                    <div
                      class="stats-card"
                      v-for="(item, index) in psssDataList"
                      :key="index"
                      @click="handleTagClick(item)"
                    >
                      <div class="stats-img">
                        <img :src="item.icon" alt="" />
                      </div>
                      <div class="stats-content">
                        <div class="stats-title">{{ item.type || '未知类型' }}</div>
                        <div class="stats-value">
                          <span style="color: white">{{ item.total || '0' }}</span
                          >/<span style="color: orange">{{ item.online || '0' }}</span
                          >/<span style="color: red">{{ item.warning || '0' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="more-link">更多</div> -->
                <!-- 表格区域 -->
                <div class="warning-table-wrapper">
                  <table class="warning-table">
                    <thead>
                      <tr>
                        <th>所属区域</th>
                        <th>报警时间</th>
                        <th>水位 (m)</th>
                        <th>报警等级</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in pssstbData" :key="index" @click="handleClickTable(item)">
                        <td>{{ item.area }}</td>
                        <td>{{ item.forecastTime }}</td>
                        <td>{{ item.waterDepth }}</td>
                        <td>{{ item.reportLevel }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="right-content-right">
            <div class="middle-center-section">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <span class="title">累计报警次数分布</span>
                </div>
                <!-- <div class="title-right">
                  <span class="area">区/县/街道</span>
                </div> -->
              </div>
              <!-- 图表区域 -->
              <div class="chart-area">
                <div ref="verticalBarChartRef" v-chart-resize class="vertical-bar-chart2"></div>
              </div>
            </div>
            <div class="middle-bottom-section">
              <!-- 标题区域 -->
              <div class="title-area">
                <div class="title-left">
                  <span class="title">处置事件</span>
                </div>
                <div class="title-right">
                  <span class="area" @click="caseInformationGET">更多</span>
                </div>
              </div>
              <!-- 表格区域 -->
              <div class="table-container">
                <table class="warning-table">
                  <thead>
                    <tr>
                      <th>处置部门</th>
                      <th>事件发生时间</th>
                      <th>事件名称</th>
                      <th>事件来源</th>
                      <th>事件状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in disposeDataList" :key="index" @click="handleClickTable(item)">
                      <td>{{ item.eventRepDepartName }}</td>
                      <td>{{ item.reportUserName }}</td>
                      <td>{{ item.rptTime }}</td>
                      <td>{{ item.eventName }}</td>
                      <td>{{ item.eventStatusName }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- 下部分 -->
            <div class="right-bottom-section">
              <!-- 标题区域 -->
              <div class="section-header">
                <div class="header-title">舆情信息</div>
                <div class="more" @click="openSentimentDialog">更多</div>
              </div>
              <!-- 告警列表容器 -->
              <div class="alert-list-container">
                <div class="alert-items-wrapper">
                  <div
                    v-for="(item, index) in sentimentDataList ? [...sentimentDataList, ...sentimentDataList] : []"
                    :key="index"
                    class="alert-item"
                  >
                    <div class="alert-content-container">
                      <div class="alert-content">{{ item.title }}</div>
                      <div class="alert-time">{{ item.time }}</div>
                    </div>
                    <div class="alert-action-container">
                      <button class="verify-button" :status="item.status">{{ item.status }}</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据查询弹窗 -->
    <el-dialog modal-class="supplies-dialog" v-model="shijiandialogVisible" title="事件处置" width="1500">
      <div style="margin-top: 40px; font-size: 24px">
        <el-form
          size="large"
          :inline="true"
          :model="formInlineshijian"
          class="demo-form-inline"
          style="margin-left: 10px"
        >
          <el-form-item label="区域">
            <el-select
              style="width: 300px"
              :popper-append-to-body="true"
              popper-class="select-popper"
              v-model="formInlineshijian.eventGridName"
              placeholder="请选择"
              clearable
            >
              <el-option :label="item.name" :key="item.id" v-for="item in shijianOptions" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间">
            <el-date-picker
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              v-model="formInlineshijian.rptTime"
              type="date"
              placeholder="请选择"
              :size="size"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmitshijian">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="shijiantableData"
          height="700"
          style="width: 100%; margin-bottom: 32px; background: transparent"
        >
          <el-table-column prop="eventRepDepartName" label="事件处置部门" />
          <el-table-column prop="reportUserName" label="事件来源" />
          <el-table-column prop="rptTime" label="发生时间" />
          <el-table-column prop="eventName" label="事件名称" />
          <el-table-column prop="eventStatusName" label="事件状态" />
          <el-table-column prop="eventGridName" label="事件位置" />
        </el-table>

        <el-pagination
          v-model:current-page="shijianPageNum"
          v-model:page-size="shijianPageSize"
          :page-sizes="[20, 40, 80, 100, 200]"
          size="large"
          layout="total, prev, pager, next, jumper"
          :total="shijianTotal"
          @size-change="handleSizeChangeshijian"
          @current-change="handleCurrentChangeshijian"
        />
      </div>
    </el-dialog>

    <!-- 应急物资弹窗 -->
    <el-dialog
      v-model="suppliesDialogVisible"
      modal-class="supplies-dialog"
      title="应急物资"
      width="1500"
      :modal="false"
      :draggable="true"
      :before-close="handleSuppliesDialogClose"
      class="custom-dialog supplies-dialog"
    >
      <div class="dialog-content">
        <!-- 上部分：筛选区域 -->
        <div class="filter-section">
          <div class="filter-item">
            <span class="filter-label" style="font-size: 22px">名称</span>
            <el-input v-model="nameValue" placeholder="请输入" clearable style="width: 300px" />
          </div>
          <div class="filter-item">
            <span class="filter-label" style="font-size: 22px">仓库</span>
            <el-dropdown @command="handleWarehouseCommand" placement="bottom-start">
              <el-button type="primary" class="custom-button warehouse-dropdown-button">
                <span class="button-text">{{
                  warehouseValue
                    ? warehouseOptions.find(item => item.id === warehouseValue)?.name || '请选择'
                    : '请选择'
                }}</span>
                <el-icon class="dropdown-icon"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="item in warehouseOptions" :key="item.id" :command="item.id">
                    {{ item.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <div class="filter-item">
            <el-button type="primary" @click="handleSuppliesSearch">查询</el-button>
            <!-- <el-button style="color: #fff" @click="handleSuppliesReset">重置</el-button> -->
          </div>
        </div>

        <!-- 下部分：表格区域 -->
        <div class="table-section supplies-table-container">
          <table class="data-table supplies-table">
            <thead>
              <tr>
                <th v-for="(column, index) in suppliesColumns" :key="index">{{ column.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, rowIndex) in suppliesData" :key="rowIndex">
                <td v-for="(column, colIndex) in suppliesColumns" :key="colIndex">
                  {{ row[column.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页器 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="suppliesPageNum"
            v-model:page-size="suppliesPageSize"
            :page-sizes="[20, 40, 80, 100, 200]"
            size="large"
            layout="total, prev, pager, next, jumper"
            :total="suppliesTotal"
            @size-change="handleSuppliesSizeChange"
            @current-change="handleSuppliesCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 防汛车辆弹窗 -->
    <!-- <el-dialog
      v-model="carDialogVisible"
      title="防汛车辆"
      width="30%"
      :modal="false"
      :before-close="carDialogClose"
      class="custom-dialog supplies-dialog"
    >
      <div class="dialog-content">

        <div class="filter-section">
          <div class="filter-item">
            <span class="filter-label">：</span>
            <el-input v-model="nameValue" placeholder="请输入车牌号或者驾驶员名称" clearable style="width: 200px" />
          </div>
          <div class="filter-item">
            <el-button type="primary" @click="handleSuppliesSearch">查询</el-button>
            <el-button style="color: #fff" @click="handleSuppliesReset">重置</el-button>
          </div>
        </div>

        <div class="table-section supplies-table-container">
          <table class="data-table supplies-table">
            <thead>
              <tr>
                <th v-for="(column, index) in carColumns" :key="index">{{ column.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, rowIndex) in carsData" :key="rowIndex">
                <td v-for="(column, colIndex) in carColumns" :key="colIndex">
                  {{ row[column.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>


        <div class="pagination-container">
          <el-pagination
            v-model:current-page="suppliesPageNum"
            v-model:page-size="suppliesPageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="suppliesTotal"
            @size-change="handleSuppliesSizeChange"
            @current-change="handleSuppliesCurrentChange"
            background
          />
        </div>
      </div>
    </el-dialog> -->

    <!-- 舆情信息弹窗 -->
    <el-dialog
      modal-class="supplies-dialog"
      v-model="sentimentDialogVisible"
      title="舆情信息"
      width="1500"
      :modal="true"
      :draggable="true"
      :before-close="handleSentimentDialogClose"
      class="custom-dialog supplies-dialog yuqing-dialog"
    >
      <div style="margin-top: 40px; font-size: 24px">
        <!-- <el-form
          size="large"
          :inline="true"
          :model="formInlineSentiment"
          class="demo-form-inline"
          style="margin-left: 10px"
        >
         
          <el-form-item label="状态">
            <el-select
              style="width: 300px"
              :popper-append-to-body="true"
              popper-class="select-popper"
              v-model="formInlineSentiment.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option :label="item.name" :key="item.value" v-for="item in sentimentStatusOptions" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmitSentiment">查询</el-button>
          </el-form-item>
        </el-form> -->
        <el-table
          :data="sentimentTableData"
          height="700"
          style="width: 100%; margin-bottom: 32px; background: transparent"
          class="sentiment-table-center"
        >
          <el-table-column prop="title" label="标题" align="center" />
          <el-table-column prop="content" label="内容" align="center" />
          <el-table-column prop="handleStatusName" label="状态" align="center" />
        </el-table>

        <el-pagination
          v-model:current-page="sentimentPageNum"
          v-model:page-size="sentimentPageSize"
          :page-sizes="[20, 40, 80, 100, 200]"
          size="large"
          layout="total, prev, pager, next, jumper"
          :total="sentimentTotal"
          @size-change="handleSentimentSizeChange"
          @current-change="handleSentimentCurrentChange"
        />
      </div>
    </el-dialog>

    <el-dialog
      modal-class="supplies-dialog"
      :modal="false"
      :draggable="true"
      v-model="fangxundialogVisible"
      title="防汛车辆"
      width="1500"
    >
      <div style="margin-top: 40px; font-size: 24px">
        <el-form
          size="large"
          :inline="true"
          :model="formInlinefangxun"
          class="demo-form-inline"
          style="margin-left: 10px"
        >
          <el-form-item label="车牌号">
            <el-input style="width: 300px" v-model="formInlinefangxun.cphm" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="驾驶⼈姓名">
            <el-input style="width: 300px" v-model="formInlinefangxun.jsrxm" placeholder="请输入" clearable />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmitfangxun">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="fangxuntableData"
          height="700"
          @row-click="handleClickfangxuancheliang"
          style="width: 100%; margin-bottom: 32px; background: transparent"
        >
          <el-table-column prop="cphm" label="车牌号" />
          <el-table-column prop="cllx" label="车辆类型" />
          <el-table-column prop="clpp" label="⻋辆品牌" />
          <el-table-column prop="jsrxm" label="驾驶⼈姓名" />
          <el-table-column prop="newDhhm" label="驾驶⼈联系电话" />
          <el-table-column prop="ssbm" label="所属部⻔" />
          <el-table-column prop="zrrxm" label="责任⼈姓名" />
        </el-table>

        <el-pagination
          v-model:current-page="fangxunPageNum"
          v-model:page-size="fangxunPageSize"
          :page-sizes="[20, 40, 80, 100, 200]"
          size="large"
          layout="total, prev, pager, next, jumper"
          :total="fangxunTotal"
          @size-change="handleSizeChangefangxun"
          @current-change="handleCurrentChangefangxun"
        />
      </div>
    </el-dialog>

    <!-- 易涝点报警弹窗 -->
    <el-dialog
      modal-class="supplies-dialog"
      v-model="yldbjDialogVisible"
      title="易涝点报警"
      width="1500"
      :modal="true"
      :draggable="true"
      :before-close="handleYldbjDialogClose"
      class="custom-dialog supplies-dialog"
    >
      <div style="margin-top: 40px; font-size: 24px">
        <el-form
          size="large"
          :inline="true"
          :model="formInlineYldbj"
          class="demo-form-inline"
          style="margin-left: 10px"
        >
          <el-form-item label="区域">
            <el-select
              style="width: 300px"
              :popper-append-to-body="true"
              popper-class="select-popper"
              v-model="formInlineYldbj.area"
              placeholder="请选择区域"
              clearable
            >
              <el-option :label="item.name" :key="item.id" v-for="item in yldbjOptions" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select
              style="width: 300px"
              :popper-append-to-body="true"
              popper-class="select-popper"
              v-model="formInlineYldbj.type"
              placeholder="请选择类型"
              clearable
            >
              <el-option :label="item.name" :key="item.value" v-for="item in yldbjTypeOptions" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmitYldbj">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="yldbjTableData" height="700" style="width: 100%; margin-bottom: 32px; background: transparent">
          <el-table-column prop="area" label="所属区域" />
          <el-table-column prop="forecastTime" label="报警时间" />
          <el-table-column prop="waterDepth" label="积水深度 (m)" />
          <el-table-column prop="reportLevel" label="报警等级" />
        </el-table>

        <el-pagination
          v-model:current-page="yldbjPageNum"
          v-model:page-size="yldbjPageSize"
          :page-sizes="[20, 40, 80, 100, 200]"
          size="large"
          layout="total, prev, pager, next, jumper"
          :total="yldbjTotal"
          @size-change="handleSizeChangeYldbj"
          @current-change="handleCurrentChangeYldbj"
        />
      </div>
    </el-dialog>

    <!-- 排水设施报警弹窗 -->
    <el-dialog
      modal-class="supplies-dialog"
      v-model="psssbjDialogVisible"
      title="排水设施报警"
      width="1500"
      :modal="true"
      :draggable="true"
      :before-close="handlePsssbjDialogClose"
      class="custom-dialog supplies-dialog"
    >
      <div style="margin-top: 40px; font-size: 24px">
        <el-form
          size="large"
          :inline="true"
          :model="formInlinePsssbj"
          class="demo-form-inline"
          style="margin-left: 10px"
        >
          <el-form-item label="区域">
            <el-select
              style="width: 300px"
              :popper-append-to-body="true"
              popper-class="select-popper"
              v-model="formInlinePsssbj.area"
              placeholder="请选择区域"
              clearable
            >
              <el-option :label="item.name" :key="item.id" v-for="item in psssbjOptions" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select
              style="width: 300px"
              :popper-append-to-body="true"
              popper-class="select-popper"
              v-model="formInlinePsssbj.type"
              placeholder="请选择类型"
              clearable
            >
              <el-option :label="item.name" :key="item.value" v-for="item in psssbjTypeOptions" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmitPsssbj">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="psssbjTableData"
          height="700"
          style="width: 100%; margin-bottom: 32px; background: transparent"
        >
          <el-table-column prop="area" label="所属区域" />
          <el-table-column prop="forecastTime" label="报警时间" />
          <el-table-column prop="waterDepth" label="水位 (m)" />
          <el-table-column prop="reportLevel" label="报警等级" />
        </el-table>

        <el-pagination
          v-model:current-page="psssbjPageNum"
          v-model:page-size="psssbjPageSize"
          :page-sizes="[20, 40, 80, 100, 200]"
          size="large"
          layout="total, prev, pager, next, jumper"
          :total="psssbjTotal"
          @size-change="handleSizeChangePsssbj"
          @current-change="handleCurrentChangePsssbj"
        />
      </div>
    </el-dialog>

    <!-- 调度指令弹窗 -->
    <el-dialog
      modal-class="supplies-dialog"
      v-model="ddzlDialogVisible"
      title="调度指令"
      width="1500"
      :modal="true"
      :draggable="true"
      :before-close="handleDdzlDialogClose"
      class="custom-dialog supplies-dialog"
    >
      <div style="margin-top: 40px; font-size: 24px">
        <el-table :data="ddzlTableData" height="700" style="width: 100%; margin-bottom: 32px; background: transparent">
          <el-table-column prop="dispatch_department" label="推送部门" />
          <el-table-column prop="dispatch_time" label="调度时间" />
          <el-table-column prop="dispatch_object" label="调度对象" />
          <el-table-column prop="dispatch_count" label="调度次数" />
          <el-table-column prop="dispatch_method" label="调度方式" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import Hls from 'hls.js'
import { ArrowDown } from '@element-plus/icons-vue'
import { cstqGET, yjcbdTable, wzwzlxGET, caseInformationPage } from '@/api/intelligence'
import {
  getCameraList,
  getEventCount,
  getPipeline,
  getScene,
  getDevice,
  getGoods,
  getflood,
  getWarning,
  getHistory,
  getSentiment,
  getEquipment,
  getDispose,
  getRegion,
  getFirstLevelWarning,
  getSecondLevelWarning,
  getThreeLevelWarning,
  getFourLevelWarning,
  getLocation,
  getEmergencySupplies,
  getPumpStation1,
  getPumpStation2,
  getPumpStation3,
  getPumpStation4,
  getPumpStation5,
  getPumpStation6,
  getImpactofwaterlogging,
  getImpactofwaterlogging1,
  getImpactofwaterlogging2,
  getLocation2,
  getMore,
  getLocation3,
  getWarehouse,
  getSupplies,
  getSentimentTable,
  getSceneScene
} from '@/api/home'

import {
  getYltj,
  getSjtj,
  getBzyx,
  getGw,
  getRywz,
  getDdzltj,
  getDdzl,
  getYldbj,
  getYldbjtb,
  getPsss,
  getPssstb,
  gettbRight,
  getddzltb,
  getsghx,
  getbcLocation,
  getwzLocation,
  getBarData,
  getMapData
} from '@/api/home-new'
import taiyuanJson from '@/assets/taiyuan.json'
// 视频点击
const handleVideoClick = item => {
  console.log(item, '视频item')
  emit('videoClicked', { tableData: item })
}

const emit = defineEmits([
  'tagClicked',
  'clickTable',
  'yuntuClickShow',
  'videoClicked',
  'tagClickedbranch',
  'clickGoods',
  'clickScene',
  'clickBc',
  'clickWz'
])
// 物资
const wzClick = async () => {
  try {
    const result = await getwzLocation()
    if (result) {
      if (result.code === 200) {
        result.rows.forEach(item => {
          ;((item.sbname = item.ckname), (item.taiyuanFlag = 'taiyuanFlag'))
        })
        emit('tagClicked', { locationData: result.rows })
      }
    }
  } catch (error) {
    console.error('物资的位置数据:', error)
  }
}

// 单个点击
const handleClickTable = item => {
  console.log(item, '表格item')
  emit('clickTable', { tableData: item })
}
const handleTagClick2 = async item => {
  console.log(item, 'item')
  const result = await getLocation2({ pageSize: 1000, category: item.category })
  console.log(result, 'result')

  // Emit the tag data and API result to the parent component
  emit('clickScene', { tagData: item, locationData: result.rows })
}

const handleTagClick = async item => {
  console.log(item, 'item')
  const result = await getLocation({ pageSize: 1000, sstype: item.type })
  console.log(result, 'result')

  // Emit the tag data and API result to the parent component
  emit('clickScene', { tagData: item, locationData: result.rows })
  try {
    const deviceres = await getSceneScene(item.type)
    console.log('场景分类下的设备', deviceres)

    if (deviceres && deviceres.data) {
      // 直接使用返回的数据，不需要JSON解析
      deviceData.value = deviceres

      // 更新设备信息模块的显示数据
      devictotalWarning.value = deviceres.totalWarning || '0'
      devictime.value = deviceres.time || '未知'

      // 设备列表数据直接使用data数组
      if (Array.isArray(deviceres.data)) {
        deviceDataList.value = deviceres.data
      } else {
        deviceDataList.value = []
      }

      console.log('设备信息模块已更新:', {
        totalWarning: devictotalWarning.value,
        totalOffline: deviceres.totalOffline,
        time: devictime.value,
        deviceList: deviceDataList.value
      })
    } else {
      console.error('获取设备数据格式不正确:', deviceres)
      // 清空设备信息模块
      deviceData.value = null
      deviceDataList.value = []
      devictotalWarning.value = '0'
      devictime.value = '未知'
    }
  } catch (error) {
    console.error('获取设备数据失败:', error)
    // 清空设备信息模块
    deviceData.value = null
    deviceDataList.value = []
    devictotalWarning.value = '0'
    devictime.value = '未知'
  }
}
const bcClick = async () => {
  try {
    const result = await getbcLocation()
    if (result) {
      if (result.code === 200) {
        console.log(result.rows, '泵车的位置数据')
        emit('clickBc', { locationData: result.rows })
      }
    }
  } catch (error) {
    console.error('泵车的位置数据:', error)
  }
}

// 处理圆圈点击事件
const handleCircleClick = async value => {
  console.log(`圆圈 ${value} 被点击`)

  try {
    // 调用 getMapData 更新 barChartRef 图表
    const mapResult = await getMapData(value)
    if (mapResult && mapResult.code === 200) {
      console.log('地图数据:', mapResult.data)
      // 更新地图图表数据
      updateBarChart(mapResult.data)
    }

    // 调用 getBarData 更新 footerChartRef 图表
    const barResult = await getBarData(value)
    if (barResult && barResult.code === 200) {
      console.log('柱状图数据:', barResult.data)
      // 更新底部图表数据
      updateFooterChart(barResult.data)
    }
  } catch (error) {
    console.error('获取圆圈点击数据失败:', error)
  }
}

// 更新地图图表数据
const updateBarChart = data => {
  if (!barChart || !data) {
    console.warn('barChart 或 data 为空，无法更新地图图表')
    return
  }

  try {
    console.log('开始更新地图图表，原始数据:', data)

    // 解析数据
    const parsedData = data
      .map(item => {
        try {
          return JSON.parse(item.value)
        } catch (e) {
          console.error('解析地图数据失败:', e, '原始数据:', item)
          return null
        }
      })
      .filter(Boolean)

    console.log('解析后的数据:', parsedData)

    if (parsedData.length > 0) {
      // 更新地图数据
      const newRegionData = parsedData[0].districts || []
      console.log('新的区域数据:', newRegionData)

      regionDataList.value = newRegionData

      // 销毁现有图表实例，然后重新初始化
      if (barChart) {
        barChart.dispose()
        barChart = null
      }
      initBarChart()
      console.log('地图图表更新成功')
    } else {
      console.warn('解析后的数据为空，无法更新地图图表')
    }
  } catch (error) {
    console.error('更新地图图表失败:', error)
  }
}

// 更新底部图表数据
const updateFooterChart = data => {
  if (!footerChart || !data) {
    console.warn('footerChart 或 data 为空，无法更新底部图表')
    return
  }

  try {
    console.log('开始更新底部图表，原始数据:', data)

    // 解析数据
    const parsedData = data
      .map(item => {
        try {
          return JSON.parse(item.value)
        } catch (e) {
          console.error('解析柱状图数据失败:', e, '原始数据:', item)
          return null
        }
      })
      .filter(Boolean)

    console.log('解析后的数据:', parsedData)

    if (parsedData.length > 0) {
      // 更新柱状图数据
      const newDistrictsData = parsedData[0].districts || []
      console.log('新的区域统计数据:', newDistrictsData)

      sjtjDatataList.value = newDistrictsData

      // 销毁现有图表实例，然后重新初始化
      if (footerChart) {
        footerChart.dispose()
        footerChart = null
      }
      initFooterChart()
      console.log('底部图表更新成功')
    } else {
      console.warn('解析后的数据为空，无法更新底部图表')
    }
  } catch (error) {
    console.error('更新底部图表失败:', error)
  }
}

const sghxData = ref(null)
const getsghxData = async () => {
  try {
    const result = await getsghx()
    if (result) {
      if (result.code === 200) {
        const data = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('三个环形数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        sghxData.value = data[0]
        console.log('三个环形:', sghxData.value)
      }
    }
  } catch (error) {
    console.error('三个环形:', error)
  }
}
const shijianTotal = ref(0)
const shijianPageNum = ref(1)
const shijianPageSize = ref(20)

const shijiandialogVisible = ref(false)
const caseInformationGET = () => {
  shijiandialogVisible.value = true
  shijianPage()
}
const formInlineshijian = ref({
  rptTime: '',
  eventGridName: ''
})
const shijianOptions = ref([
  { name: '尖草坪区', value: '尖草坪区' },
  { name: '杏花岭区', value: '杏花岭区' },
  { name: '小店区', value: '小店区' },
  { name: '迎泽区', value: '迎泽区' },
  { name: '万柏林区', value: '万柏林区' },
  { name: '晋源区', value: '晋源区' }
])
const shijiantableData = ref()
const shijianPage = () => {
  caseInformationPage(
    shijianPageNum.value,
    shijianPageSize.value,
    formInlineshijian.value.eventGridName,
    formInlineshijian.value.rptTime
  ).then(res => {
    console.log(res, '事件分页')
    if (res.code === 200) {
      shijiantableData.value = res.rows
    }
  })
}
const onSubmitshijian = () => {
  console.log(formInlineshijian.value)

  shijianPageNum.value = 1
  shijianPage()
}
const handleSizeChangeshijian = size => {
  shijianPageSize.value = size
  shijianPageNum.value = 1
  shijianPage()
}
const handleCurrentChangeshijian = page => {
  shijianPageNum.value = page
  shijianPage()
}

// 易涝点报警弹窗相关
const yldbjDialogVisible = ref(false)
const yldbjTotal = ref(0)
const yldbjPageNum = ref(1)
const yldbjPageSize = ref(20)
const yldbjTableData = ref([])
const formInlineYldbj = ref({
  area: '',
  type: ''
})
const yldbjOptions = ref([
  { name: '尖草坪区', id: 1 },
  { name: '杏花岭区', id: 2 },
  { name: '小店区', id: 3 },
  { name: '迎泽区', id: 4 },
  { name: '万柏林区', id: 5 },
  { name: '晋源区', id: 6 }
])
const yldbjTypeOptions = ref([
  { name: '低洼路段', value: '低洼路段' },
  { name: '人行下穿', value: '人行下穿' },
  { name: '车辆下穿', value: '车辆下穿' }
])

// 打开易涝点报警弹窗
const openYldbjDialog = () => {
  yldbjDialogVisible.value = true
  yldbjPage()
}

// 关闭易涝点报警弹窗
const handleYldbjDialogClose = () => {
  yldbjDialogVisible.value = false
}

// 获取易涝点报警数据
const yldbjPage = () => {
  const params = {
    pageNum: yldbjPageNum.value,
    pageSize: yldbjPageSize.value
  }

  // 添加查询条件
  if (formInlineYldbj.value.area) {
    params.area = formInlineYldbj.value.area
  }
  if (formInlineYldbj.value.type) {
    params.type = formInlineYldbj.value.type
  }

  gettbRight(params)
    .then(res => {
      console.log(res, '易涝点报警分页数据')
      if (res.code === 200) {
        yldbjTableData.value = res.rows || []
        yldbjTotal.value = res.total || 0
      }
    })
    .catch(error => {
      console.error('获取易涝点报警数据失败:', error)
      // 如果接口调用失败，设置一些模拟数据用于测试
      yldbjTableData.value = [
        {
          area: '尖草坪区',
          forecastTime: '2024-01-15 10:30:00',
          waterDepth: '0.5',
          reportLevel: '黄色预警',
          location: '西中环南延',
          status: '处理中'
        },
        {
          area: '小店区',
          forecastTime: '2024-01-15 11:15:00',
          waterDepth: '0.8',
          reportLevel: '橙色预警',
          location: '长风街',
          status: '已处理'
        }
      ]
      yldbjTotal.value = 2
    })
}

// 查询易涝点报警
const onSubmitYldbj = () => {
  console.log('查询条件:', formInlineYldbj.value)
  yldbjPageNum.value = 1
  yldbjPage()
}

// 分页大小改变
const handleSizeChangeYldbj = size => {
  yldbjPageSize.value = size
  yldbjPageNum.value = 1
  yldbjPage()
}

// 当前页改变
const handleCurrentChangeYldbj = page => {
  yldbjPageNum.value = page
  yldbjPage()
}

// 排水设施报警弹窗相关
const psssbjDialogVisible = ref(false)
const psssbjTotal = ref(0)
const psssbjPageNum = ref(1)
const psssbjPageSize = ref(20)
const psssbjTableData = ref([])
const formInlinePsssbj = ref({
  area: '',
  type: ''
})
const psssbjOptions = ref([
  { name: '尖草坪区', id: 1 },
  { name: '杏花岭区', id: 2 },
  { name: '小店区', id: 3 },
  { name: '迎泽区', id: 4 },
  { name: '万柏林区', id: 5 },
  { name: '晋源区', id: 6 }
])
const psssbjTypeOptions = ref([
  { name: '雨水管网', value: '雨水管网' },
  { name: '缓洪池', value: '缓洪池' },
  { name: '雨水泵站', value: '雨水泵站' },
  { name: '排水渠', value: '排水渠' },
  { name: '汾河入口', value: '汾河入口' },
  { name: '河道', value: '河道' }
])

// 打开排水设施报警弹窗
const openPsssbjDialog = () => {
  psssbjDialogVisible.value = true
  psssbjPage()
}

// 关闭排水设施报警弹窗
const handlePsssbjDialogClose = () => {
  psssbjDialogVisible.value = false
}

// 获取排水设施报警数据
const psssbjPage = () => {
  const params = {
    pageNum: psssbjPageNum.value,
    pageSize: psssbjPageSize.value
  }

  // 添加查询条件
  if (formInlinePsssbj.value.area) {
    params.area = formInlinePsssbj.value.area
  }
  if (formInlinePsssbj.value.type) {
    params.type = formInlinePsssbj.value.type
  }

  gettbRight(params)
    .then(res => {
      console.log(res, '排水设施报警分页数据')
      if (res.code === 200) {
        psssbjTableData.value = res.rows || []
        psssbjTotal.value = res.total || 0
      }
    })
    .catch(error => {
      console.error('获取排水设施报警数据失败:', error)
      // 如果接口调用失败，设置一些模拟数据用于测试
      psssbjTableData.value = [
        {
          area: '尖草坪区',
          forecastTime: '2024-01-15 10:30:00',
          waterDepth: '1.2',
          reportLevel: '黄色预警',
          location: '西中环泵站',
          status: '处理中'
        },
        {
          area: '小店区',
          forecastTime: '2024-01-15 11:15:00',
          waterDepth: '1.8',
          reportLevel: '橙色预警',
          location: '长风街排水口',
          status: '已处理'
        }
      ]
      psssbjTotal.value = 2
    })
}

// 查询排水设施报警
const onSubmitPsssbj = () => {
  console.log('查询条件:', formInlinePsssbj.value)
  psssbjPageNum.value = 1
  psssbjPage()
}

// 分页大小改变
const handleSizeChangePsssbj = size => {
  psssbjPageSize.value = size
  psssbjPageNum.value = 1
  psssbjPage()
}

// 当前页改变
const handleCurrentChangePsssbj = page => {
  psssbjPageNum.value = page
  psssbjPage()
}

// 调度指令弹窗相关
const ddzlDialogVisible = ref(false)
const ddzlTableData = ref([])

// 打开调度指令弹窗
const openDdzlDialog = () => {
  console.log('调度指令更多按钮被点击')
  ddzlDialogVisible.value = true
  loadDdzlData()
}

// 将函数暴露到全局，用于调试
if (typeof window !== 'undefined') {
  window.testOpenDdzlDialog = openDdzlDialog
}

// 关闭调度指令弹窗
const handleDdzlDialogClose = () => {
  ddzlDialogVisible.value = false
}

// 加载调度指令数据
const loadDdzlData = () => {
  console.log('开始加载调度指令数据')
  getddzltb()
    .then(res => {
      console.log(res, '调度指令弹窗数据')
      if (res.code === 200) {
        const data = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('易涝点报警数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        ddzlTableData.value = data[0].data
        console.log('调度指令数据加载成功:', ddzlTableData.value)
      }
    })
    .catch(error => {
      console.error('获取调度指令数据失败:', error)
      // 如果接口调用失败，设置一些模拟数据用于测试
      ddzlTableData.value = [
        {
          dispatch_department: '排管中心',
          dispatch_time: '2025-01-15 10:30:00',
          dispatch_object: '泵站A',
          dispatch_count: 3,
          dispatch_method: '语音'
        },
        {
          dispatch_department: '应急指挥中心',
          dispatch_time: '2025-01-15 11:15:00',
          dispatch_object: '抢险队伍',
          dispatch_count: 2,
          dispatch_method: '平台推送'
        },
        {
          dispatch_department: '水务局',
          dispatch_time: '2025-01-15 12:00:00',
          dispatch_object: '调蓄池B',
          dispatch_count: 1,
          dispatch_method: '短信'
        },
        {
          dispatch_department: '排管中心',
          dispatch_time: '2025-01-15 13:30:00',
          dispatch_object: '管网维护',
          dispatch_count: 4,
          dispatch_method: '视频'
        },
        {
          dispatch_department: '应急指挥中心',
          dispatch_time: '2025-01-15 14:45:00',
          dispatch_object: '救援车辆',
          dispatch_count: 2,
          dispatch_method: '语音'
        }
      ]
    })
}

// 易涝点报警表格
const pssstbData = ref(null)
const getPssstbData = async () => {
  try {
    const result = await getPssstb()
    if (result) {
      if (result.code === 200) {
        pssstbData.value = result.rows
        // console.log('易涝点报警表格:', yldbjtbData.value)
      }
    }
  } catch (error) {
    console.error('易涝点报警:', error)
  }
}

// 排水设施报警
const psssData = ref(null)
const psssDataList = ref(null)
const pssstotalWarning = ref(null)
const getpsssData = async () => {
  try {
    const result = await getPsss()
    if (result) {
      if (result.code === 200) {
        psssData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('易涝点报警数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        psssDataList.value = psssData.value[0].data
        pssstotalWarning.value = psssData.value[0].totalWarning
        // console.log('易涝点报警:', yldbjDataList.value)
      }
    }
  } catch (error) {
    console.error('易涝点报警:', error)
  }
}

// 易涝点报警表格
const yldbjtbData = ref(null)
const getyldbjtbData = async () => {
  try {
    const result = await getYldbjtb()
    if (result) {
      if (result.code === 200) {
        yldbjtbData.value = result.rows
        // console.log('易涝点报警表格:', yldbjtbData.value)
      }
    }
  } catch (error) {
    console.error('易涝点报警:', error)
  }
}

// 易涝点报警
const yldbjData = ref(null)
const yldbjDataList = ref(null)
const yldbjtotalWarning = ref(null)
const getyldbjData = async () => {
  try {
    const result = await getYldbj()
    if (result) {
      if (result.code === 200) {
        yldbjData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('易涝点报警数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        yldbjDataList.value = yldbjData.value[0].data
        yldbjtotalWarning.value = yldbjData.value[0].totalWarning
        // console.log('易涝点报警:', yldbjDataList.value)
      }
    }
  } catch (error) {
    console.error('易涝点报警:', error)
  }
}

// 调度指令
const ddzlData = ref(null)
const ddzlDataList = ref(null)
const getDdzlData = async () => {
  try {
    const result = await getDdzl()
    if (result) {
      if (result.code === 200) {
        ddzlData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('易涝点报警数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        ddzlDataList.value = ddzlData.value[0].data
        // console.log('调度指令:',   ddzlDataList.value)
      }
    }
  } catch (error) {
    console.error('调度指令:', error)
  }
}

// 调度指令统计
const ddzltjData = ref(null)
const ddzltjDataList = ref(null)
const total_scheduling = ref(null)
const getDdzltjData = async () => {
  try {
    const result = await getDdzltj()
    if (result) {
      if (result.code === 200) {
        ddzltjData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('调度指令统计数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        ddzltjDataList.value = ddzltjData.value[0].details
        total_scheduling.value = ddzltjData.value[0].total_scheduling
        // console.log('调度指令统计:',   ddzltjDataList.value)

        // 数据加载完成后重新初始化图表
        nextTick(() => {
          initFooterChart() // 重新初始化底部图表
          initDispatchChart() // 重新初始化调度统计图表
        })
      }
    }
  } catch (error) {
    console.error('调度指令统计:', error)
  }
}

// 人员物资
const rywzData = ref(null)
const flood_control_personnel = ref(null)
const emergency_pump_trucks = ref(null)
const flood_control_vehicles = ref(null)
const flood_control_materials = ref(null)
const getRywzData = async () => {
  try {
    const result = await getRywz()
    if (result) {
      if (result.code === 200) {
        rywzData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('人员物资数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        flood_control_personnel.value = rywzData.value[0].flood_control_personnel
        emergency_pump_trucks.value = rywzData.value[0].emergency_pump_trucks
        flood_control_vehicles.value = rywzData.value[0].flood_control_vehicles
        flood_control_materials.value = rywzData.value[0].flood_control_materials
        // console.log('人员物资:', emergency_pump_trucks.value)
      }
    }
  } catch (error) {
    console.error('人员物资:', error)
  }
}

// 管网
const gwData = ref(null)
const pipeline_health = ref(null)
const buffer_pool_total = ref(null)
const buffer_pool_used = ref(null)
const drainage_channel_total = ref(null)
const drainage_channel_used = ref(null)
const pipeline_total_length = ref(null)
const getGwData = async () => {
  try {
    const result = await getGw()
    if (result) {
      if (result.code === 200) {
        gwData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('管网数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据

        if (gwData.value && gwData.value[0]) {
          pipeline_health.value = gwData.value[0].pipeline_health
          buffer_pool_total.value = gwData.value[0].buffer_pool_total
          buffer_pool_used.value = gwData.value[0].buffer_pool_used
          drainage_channel_total.value = gwData.value[0].drainage_channel_total
          drainage_channel_used.value = gwData.value[0].drainage_channel_used
          pipeline_total_length.value = gwData.value[0].pipeline_total_length

          // 数据加载完成后重新初始化管网健康度图表
          nextTick(() => {
            initOilWaterLevelChart()
          })
        }
      }
    }
  } catch (error) {
    console.error('管网数据失败:', error)
  }
}

// 泵站运行
const bzyxData = ref(null)
const bzyxDatataList = ref(null)
const total_pumps = ref(null)
const running_pumps = ref(null)
const total_pumping_capacity = ref(null)
const running_pumping_capacity = ref(null)
const getBzyxData = async () => {
  try {
    const result = await getBzyx()
    if (result) {
      if (result.code === 200) {
        bzyxData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('泵站运行数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        bzyxDatataList.value = bzyxData.value[0].status_distribution
        total_pumps.value = bzyxData.value[0].total_pumps
        running_pumps.value = bzyxData.value[0].running_pumps
        total_pumping_capacity.value = bzyxData.value[0].total_pumping_capacity
        running_pumping_capacity.value = bzyxData.value[0].running_pumping_capacity
        // console.log('泵站运行:',    running_pumping_capacity.value = bzyxData.value[0])

        // 数据加载完成后重新初始化泵站运行图表
        nextTick(() => {
          initPumpRunningChart()
        })
      }
    }
  } catch (error) {
    console.error('泵站运行:', error)
  }
}

// 事件统计
const sjtjData = ref(null)
const sjtjDatataList = ref(null)
const getSjtjData = async () => {
  try {
    const result = await getSjtj()
    if (result) {
      if (result.code === 200) {
        sjtjData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('事件统计数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        sjtjDatataList.value = sjtjData.value[0].districts
        // console.log('事件统计:',   sjtjDatataList.value)

        // 数据加载完成后重新初始化图表
        nextTick(() => {
          initFooterChart() // 重新初始化底部图表
        })
      }
    }
  } catch (error) {
    console.error('事件统计:', error)
  }
}

// 雨量统计
const yltjData = ref(null)
const yltjDataList = ref(null)
const current_rainfall = ref(null)
const total_rainfall = ref(null)
const getYltjData = async () => {
  try {
    const result = await getYltj()
    if (result) {
      if (result.code === 200) {
        yltjData.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('雨量统计数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        yltjDataList.value = yltjData.value[0].districts
        current_rainfall.value = yltjData.value[0].current_rainfall
        total_rainfall.value = yltjData.value[0].total_rainfall
        // console.log('雨量统计:',   yltjDataList.value)

        // 数据加载完成后重新初始化降雨图表
        nextTick(() => {
          initRainfallChart()
        })
      }
    }
  } catch (error) {
    console.error('雨量统计:', error)
  }
}

const fangxundialogVisible = ref(false)
const fangxunPageNum = ref(1)
const fangxunPageSize = ref(20)
const fangxunTotal = ref(0)
const formInlinefangxun = ref({
  cphm: '',
  jsrxm: ''
})

const wuItemClick = item => {
  console.log(item, '物资的单个点击')
  wzwzlxGET(item.type).then(res => {
    console.log(res, '物资的单个点击数据返回')
    res.rows.forEach(item => {
      ;((item.sbname = item.ckname), (item.taiyuanFlag = 'taiyuanFlag'))
    })
    emit('tagClicked', { tagData: item, locationData: res.rows })
  })
}
const fangxuntableData = ref([])

// 泵站数据
const pumpStationData = ref({
  totalStations: '300',
  totalCapacity: '5000',
  runningStations: '100',
  runningCapacity: '4000'
})

// 蓄洪池数据
const reservoirData = ref({
  totalCapacity: '3',
  drainageCapacity: '3',
  usedCapacity: '1',
  usedDrainageCapacity: '3'
})

// 应急物资更多
const goodsMore = () => {
  console.log('更多')
  suppliesDialogVisible.value = true
  loadSuppliesData() // 首次打开弹窗加载数据
}

// 应急物资弹窗相关
const suppliesDialogVisible = ref(false)
const warehouseValue = ref('')
const nameValue = ref('')
const warehouseOptions = ref([
  { label: '全部', value: 'all' },
  { label: '发改委', value: '发改委' },
  { label: '消防支队', value: '消防支队' },
  { label: '尖草坪', value: '尖草坪' },
  { label: '房产局', value: '房产局' },
  { label: '国资委', value: '国资委' },
  { label: '城管局', value: '城管局' },
  { label: '水务局', value: '水务局' },
  { label: '万柏林', value: '万柏林' },
  { label: '迎泽', value: '迎泽' },
  { label: '园林局', value: '园林局' },
  { label: '杏花岭', value: '杏花岭' },
  { label: '晋源', value: '晋源' },
  { label: '古交', value: '古交' },
  { label: '清徐', value: '清徐' },
  { label: '阳曲', value: '阳曲' },
  { label: '娄烦', value: '娄烦' },
  { label: '综改', value: '综改' },
  { label: '中北', value: '中北' },
  { label: '西山', value: '西山' }
])

const suppliesColumns = ref([
  { label: '名称', prop: 'wzlx' },
  { label: '仓库', prop: 'ckname' },
  { label: '单位', prop: 'jldw' },
  { label: '数量', prop: 'sl' }
])
const suppliesData = ref([])
const suppliesPageNum = ref(1)
const suppliesPageSize = ref(20)
const suppliesTotal = ref(0)

// 不再需要本地分页计算，直接使用API返回的数据

// 关闭应急物资弹窗
const handleSuppliesDialogClose = () => {
  suppliesDialogVisible.value = false
}
const carDialogClose = () => {
  carDialogVisible.value = false
}

// 应急物资筛选条件变更
const handleSuppliesSearch = () => {
  suppliesPageNum.value = 1 // 重置为第一页
  loadSuppliesData()
}

// 应急物资重置按钮
const handleSuppliesReset = () => {
  warehouseValue.value = ''
  nameValue.value = ''
  suppliesPageNum.value = 1 // 重置为第一页
  loadSuppliesData()
}

// 仓库下拉框选择处理
const handleWarehouseCommand = command => {
  warehouseValue.value = command
}

// 分页大小变化处理
const handleSuppliesSizeChange = size => {
  suppliesPageSize.value = size
  console.log('分页大小变化处理', size)
  suppliesPageNum.value = 1 // 切换每页大小时重置为第一页
  loadSuppliesData()
}

// 当前页变化处理
const handleSuppliesCurrentChange = page => {
  suppliesPageNum.value = page
  loadSuppliesData()
}

// 加载应急物资表格数据
const loadSuppliesData = async () => {
  console.log('获取应急物资数据')

  // 构建查询参数
  const params = {
    pageNum: suppliesPageNum.value,
    pageSize: suppliesPageSize.value // 修改为更大的值，确保能获取更多数据
  }

  if (warehouseValue.value && warehouseValue.value !== 'all') {
    params.cbdId = warehouseValue.value
    console.log('查询仓库', warehouseValue.value.trim())
  }

  if (nameValue.value && nameValue.value.trim() !== '') {
    console.log('查询名称', nameValue.value.trim())
    params.wzlx = nameValue.value.trim()
  }

  try {
    // 尝试调用API获取数据
    const res = await getSupplies(params)

    if (res.code === 200) {
      suppliesData.value = res.rows
      suppliesTotal.value = res.total || res.rows.length
      console.log('应急物资数据总数:', suppliesTotal.value)
    } else {
      // 如果API不可用，使用模拟数据
      const mockData = [
        { wzlx: '救生衣', ckname: '中央仓库', sl: 200, jldw: '件' },
        { wzlx: '救生圈', ckname: '中央仓库', sl: 150, jldw: '个' },
        { wzlx: '雨衣', ckname: '北区仓库', sl: 300, jldw: '件' },
        { wzlx: '雨靴', ckname: '北区仓库', sl: 300, jldw: '双' },
        { wzlx: '抽水泵', ckname: '南区仓库', sl: 50, jldw: '台' },
        { wzlx: '发电机', ckname: '南区仓库', sl: 20, jldw: '台' },
        { wzlx: '沙袋', ckname: '东区仓库', sl: 5000, jldw: '个' },
        { wzlx: '编织袋', ckname: '东区仓库', sl: 10000, jldw: '个' },
        { wzlx: '铁锹', ckname: '西区仓库', sl: 200, jldw: '把' },
        { wzlx: '手电筒', ckname: '西区仓库', sl: 100, jldw: '个' },
        { wzlx: '对讲机', ckname: '中央仓库', sl: 50, jldw: '台' },
        { wzlx: '应急灯', ckname: '中央仓库', sl: 80, jldw: '个' },
        { wzlx: '帐篷', ckname: '东区仓库', sl: 30, jldw: '顶' },
        { wzlx: '毛毯', ckname: '东区仓库', sl: 200, jldw: '条' },
        { wzlx: '应急食品', ckname: '南区仓库', sl: 500, jldw: '箱' },
        { wzlx: '饮用水', ckname: '南区仓库', sl: 1000, jldw: '箱' },
        { wzlx: '医疗箱', ckname: '西区仓库', sl: 50, jldw: '个' },
        { wzlx: '防护服', ckname: '西区仓库', sl: 100, jldw: '套' },
        { wzlx: '安全帽', ckname: '北区仓库', sl: 200, jldw: '个' },
        { wzlx: '防毒面具', ckname: '北区仓库', sl: 100, jldw: '个' },
        { wzlx: '灭火器', ckname: '中央仓库', sl: 150, jldw: '个' },
        { wzlx: '消防水带', ckname: '中央仓库', sl: 80, jldw: '卷' },
        { wzlx: '担架', ckname: '东区仓库', sl: 40, jldw: '副' },
        { wzlx: '急救药品', ckname: '东区仓库', sl: 100, jldw: '箱' },
        { wzlx: '防汛板', ckname: '南区仓库', sl: 200, jldw: '块' }
      ]

      // 根据筛选条件过滤数据
      let filteredData = [...mockData]

      if (warehouseValue.value && warehouseValue.value !== 'all') {
        const warehouseName = warehouseOptions.value.find(item => item.value === warehouseValue.value)?.label
        filteredData = filteredData.filter(item => item.ckname === warehouseName)
      }

      if (nameValue.value && nameValue.value.trim() !== '') {
        const searchText = nameValue.value.trim().toLowerCase()
        filteredData = filteredData.filter(item => item.wzlx.toLowerCase().includes(searchText))
      }

      suppliesData.value = filteredData
      suppliesTotal.value = filteredData.length
    }

    console.log('应急物资数据', suppliesData.value)
  } catch (e) {
    console.log('获取应急物资数据失败', e)
    // 发生错误时使用模拟数据
    const mockData = [
      { wzlx: '救生衣', ckname: '中央仓库', sl: 200, jldw: '件' },
      { wzlx: '救生圈', ckname: '中央仓库', sl: 150, jldw: '个' },
      { wzlx: '雨衣', ckname: '北区仓库', sl: 300, jldw: '件' },
      { wzlx: '雨靴', ckname: '北区仓库', sl: 300, jldw: '双' }
    ]
    suppliesData.value = mockData
    suppliesTotal.value = mockData.length
  }
}

// 应急物资
const clickEmergency = async item => {
  console.log('应急物资', item)
  const result = await getWarehouse(item.type)
  if (result.code === 200) {
    emit('clickGoods', { tagData: item, locationData: result.rows })
  }
}

// 防汛车辆
const carDialogVisible = ref(false)
const carsData = ref([])
const carColumns = ref([
  { label: '车牌号', prop: 'cphm' },
  { label: '车辆类型', prop: 'cllx' },
  { label: '⻋辆品牌', prop: 'clpp' },
  { label: '驾驶⼈姓名', prop: 'jsrxm' },
  { label: '驾驶⼈联系电话', prop: 'jsrlxdh' },
  { label: '所属部⻔', prop: 'ssbm' },
  { label: '责任⼈姓名', prop: 'zrrxm' }
])

const onSubmitfangxun = () => {
  fangxunPageNum.value = 1
  fangxuncheliangFunction()
}
const handleSizeChangefangxun = size => {
  fangxunPageNum.value = 1
  fangxunPageSize.value = size
  fangxuncheliangFunction()
}
const handleCurrentChangefangxun = page => {
  fangxunPageNum.value = page
  fangxuncheliangFunction()
}
const fangxuncheliangFunction = async () => {
  const result = await getLocation3(
    fangxunPageNum.value,
    fangxunPageSize.value,
    formInlinefangxun.value.cphm,
    formInlinefangxun.value.jsrxm
  )
  if (result.code === 200) {
    console.log(result, 'fangxunceres')
    // carDialogVisible.value = true
    // fangxundialogVisible.value = true
    // carsData.value = result.rows
    fangxuntableData.value = result.rows
    result.rows.forEach(resData => {
      resData.newDhhm = resData.jsrlxdh.slice(0, 3) + '****' + resData.jsrlxdh.slice(resData.jsrlxdh.length - 4)
    })
    fangxunTotal.value = result.total
    emit('tagClickedbranch', { tagData: floodvehicle.value, locationData: result.rows })
  }
}

const handleClickfangxuancheliang = item => {
  console.log(item, fdapi, '点击单个车辆')
  fdapi.marker.focus('FXCL_' + item.sbbh, 1200, 0)
}
const getCarLocation = item => {
  fangxuncheliangFunction()
}

// 弹窗相关
const dialogVisible = ref(false)
const dateTimeValue = ref('')
const currentFilter = ref('全部')
const filterOptions = ref([
  { label: '全部', value: 'all' },
  { label: '尖草坪区', value: '尖草坪区' },
  { label: '万柏林区', value: '万柏林区' },
  { label: '晋源区', value: '晋源区' },
  { label: '小店区', value: '小店区' },
  { label: '迎泽区', value: '迎泽区' },
  { label: '杏花岭区', value: '杏花岭区' }
])
const tableColumns = ref([
  { label: '事件处置部门', prop: 'rptUserName' },
  { label: '上报人', prop: 'eventName' },
  { label: '事件发生时间', prop: 'eventdispResponseDeadline' },
  { label: '事件名称', prop: 'eventTypeName' },
  { label: '事件状态', prop: 'eventLevelName' },
  { label: '事件位置', prop: 'eventGridName' }
])

const tableData = ref([])
const totalItems = ref(0)

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true
  loadTableData() // 首次打开弹窗加载数据
}

// 关闭弹窗
const handleDialogClose = () => {
  dialogVisible.value = false
}

// 筛选条件变更
const handleFilterCommand = command => {
  currentFilter.value = filterOptions.value.find(item => item.value === command)?.label || '全部'
  // loadTableData() // 重新加载数据
}

// 查询按钮
const handleSearch = () => {
  loadTableData()
}

// 重置按钮
const handleReset = () => {
  dateTimeValue.value = ''
  currentFilter.value = '全部'
  params.value = null
  loadTableData()
}
const params = ref(null)
// 加载表格数据
const loadTableData = async () => {
  console.log('获取更多数据')
  // 下拉框不是全部时才传
  if (currentFilter.value !== '全部') {
    // 取下拉框的value作为eventGridName
    const filterType = filterOptions.value.find(item => item.label === currentFilter.value)?.label
    params.value.eventGridName = currentFilter.value
  }
  // 日期有值时才传
  if (dateTimeValue.value) {
    params.value.rptTime = dateTimeValue.value
  }

  // 调用getMore
  try {
    const res = await getMore(params.value)
    console.log('获取更多数据', res)
    if (res && res.code === 200) {
      tableData.value = res.rows
      // totalItems.value = res.data.total || res.data.rows.length
      console.log('更多数据', res)
    } else {
      tableData.value = []
      totalItems.value = 0
    }
  } catch (e) {
    console.log('获取更多数据失败', e)
  }
}

// 根据标题获取背景类名
const getBackgroundClass = title => {
  if (title === '轻度影响') {
    return 'bg-light'
  } else if (title === '较重影响') {
    return 'bg-medium'
  } else if (title === '重度影响') {
    return 'bg-heavy'
  }
  return 'bg-light' // 默认背景
}

// 内涝影响分析
const impactofwaterlogging2 = ref(null)
const impactofwaterlogging2List = ref(null)
const getImpactofwaterlogging2Fc = async () => {
  try {
    const result = await getImpactofwaterlogging2()
    console.log('6666666', result)
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterlogging2.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterlogging2List.value = impactofwaterlogging2.value[0].data[0]
        // console.log('11111111111111112222222222333333:', impactofwaterlogging2.value[0].data[0].time)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 内涝影响分析
const impactofwaterlogging1 = ref(null)
const impactofwaterlogging1List = ref(null)
const impactofwaterlogging1details = ref(null)
const impactofwaterlogging1summary = ref(null)
const getImpactofwaterlogging1Fc = async () => {
  try {
    const result = await getImpactofwaterlogging1()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterlogging1.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterlogging1List.value = impactofwaterlogging1.value[0].data
        impactofwaterlogging1details.value = impactofwaterlogging1.value[0].data.details
        impactofwaterlogging1summary.value = impactofwaterlogging1.value[0].data.summary
        console.log('数据数据数据数据数据数据数据:', impactofwaterlogging1.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 内涝影响分析
const impactofwaterlogging = ref(null)
const impactofwaterloggingList = ref(null)
const getImpactofwaterloggingFc = async () => {
  try {
    const result = await getImpactofwaterlogging()
    if (result && result.data && Array.isArray(result.data)) {
      if (result.code === 200) {
        impactofwaterlogging.value = result.data
          .map(item => {
            try {
              return JSON.parse(item.value)
            } catch (e) {
              console.error('解析事件数据失败:', e)
              return null
            }
          })
          .filter(Boolean) // 过滤掉解析失败的数据
        impactofwaterloggingList.value = impactofwaterlogging.value[0].data
        // console.log('内涝预报数据:', impactofwaterlogging.value)
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 预警信息严重程度分级
const pumpStation6List = ref(null)
const pumpStation6ListData = ref(null)
const getPumpStation6Data = async () => {
  try {
    const result = await getPumpStation6()

    if (result && result.data && Array.isArray(result.data)) {
      pumpStation6List.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pumpStation6ListData.value = pumpStation6List.value[0].data
      console.log('1111111111111111加载成功:', pumpStation6ListData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 易涝点统计
const pumpStation5List = ref(null)
const pumpStation5ListData = ref(null)
const getPumpStation5Data = async () => {
  try {
    const result = await getPumpStation5()

    if (result && result.data && Array.isArray(result.data)) {
      pumpStation5List.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pumpStation5ListData.value = pumpStation5List.value[0].data
      console.log('1111111111111111加载成功:', pumpStation5ListData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 抽排量
const pumpStation4List = ref(null)
const pumpStation4ListData = ref(null)
const getPumpStation4Data = async () => {
  try {
    const result = await getPumpStation4()

    if (result && result.data && Array.isArray(result.data)) {
      pumpStation4List.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pumpStation4ListData.value = pumpStation4List.value[0].data
      // console.log('1111111111111111加载成功:', pumpStation4ListData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 前池水位
const pumpStation3List = ref(null)
const pumpStation3ListData = ref(null)
const getPumpStation3Data = async () => {
  try {
    const result = await getPumpStation3()

    if (result && result.data && Array.isArray(result.data)) {
      pumpStation3List.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pumpStation3ListData.value = pumpStation3List.value[0].data
      // console.log('1111111111111111加载成功:', pumpStation3ListData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 泵站运行时间统计
const pumpStation2List = ref(null)
const pumpStation2ListData = ref(null)
const getPumpStation2Data = async () => {
  try {
    const result = await getPumpStation2()

    if (result && result.data && Array.isArray(result.data)) {
      pumpStation2List.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pumpStation2ListData.value = pumpStation2List.value[0].data
      // console.log('1111111111111111加载成功:', pumpStation2ListData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 泵站运行统计
const pumpStation1List = ref(null)
const pumpStation1ListData = ref(null)
const getPumpStation1Data = async () => {
  try {
    const result = await getPumpStation1()

    if (result && result.data && Array.isArray(result.data)) {
      pumpStation1List.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pumpStation1ListData.value = pumpStation1List.value[0].data
      // console.log('1111111111111111加载成功:', pumpStation1List.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 应急物资
const emergencySuppliesList = ref(null)
const emergencySuppliesTotal = ref(null)
const emergencySuppliesListData = ref(null)
const getEmergencySuppliesData = async () => {
  try {
    const result = await getEmergencySupplies()

    if (result && result.data && Array.isArray(result.data)) {
      emergencySuppliesList.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      emergencySuppliesTotal.value = emergencySuppliesList.value[0].total
      emergencySuppliesListData.value = emergencySuppliesList.value[0].data
      // console.log('1111111111111111加载成功:', emergencySuppliesList.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

const tab = ref('realTime')
const changeTab = newTab => {
  tab.value = newTab
  // 切换标签后重新加载当前选中的预警数据
  loadSelectedWarningData()
}

// 当前选中的预警级别
const selectedWarningLevel = ref('一级预警')

// 加载选中的预警数据
const loadSelectedWarningData = () => {
  // 根据当前选中的预警级别和tab状态加载对应数据
  let warningData = null

  switch (selectedWarningLevel.value) {
    case '一级预警':
      warningData = tab.value === 'realTime' ? firstLevelWarningn.value : firstLevelWarninh.value
      break
    case '二级预警':
      warningData = tab.value === 'realTime' ? SecondLevelWarningn.value : SecondLevelWarninh.value
      break
    case '三级预警':
      warningData = tab.value === 'realTime' ? ThreeLevelWarningn.value : ThreeLevelWarninh.value
      break
    case '四级预警':
      warningData = tab.value === 'realTime' ? FourLevelWarningn.value : FourLevelWarninh.value
      break
    default:
      warningData = tab.value === 'realTime' ? firstLevelWarningn.value : firstLevelWarninh.value
  }

  // 更新表格数据
  if (warningData && Array.isArray(warningData)) {
    topWarningList.value = warningData
  } else {
    console.error('预警数据格式不正确:', warningData)
    topWarningList.value = []
  }
}

// 一级预警
const firstLevelWarning = ref(null)
const firstLevelWarninh = ref(null)
const firstLevelWarningn = ref(null)
const getFirstLevelWarningData = async () => {
  try {
    const result = await getFirstLevelWarning()
    if (result && result.data && Array.isArray(result.data)) {
      firstLevelWarning.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据

      if (firstLevelWarning.value && firstLevelWarning.value[0]) {
        firstLevelWarninh.value = firstLevelWarning.value[0].history
        firstLevelWarningn.value = firstLevelWarning.value[0].real_time
        console.log('111111111111111122222222222加载成功历史:', firstLevelWarninh.value)
        console.log('111111111111111122222222222加载成功·实时:', firstLevelWarningn.value)

        // 默认加载一级预警的实时数据
        if (selectedWarningLevel.value === '一级预警' && tab.value === 'realTime') {
          loadSelectedWarningData()
        }
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 二级预警
const SecondLevelWarning = ref(null)
const SecondLevelWarninh = ref(null)
const SecondLevelWarningn = ref(null)
const getSecondLevelWarningData = async () => {
  try {
    const result = await getSecondLevelWarning()
    if (result && result.data && Array.isArray(result.data)) {
      SecondLevelWarning.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据

      if (SecondLevelWarning.value && SecondLevelWarning.value[0]) {
        SecondLevelWarninh.value = SecondLevelWarning.value[0].history
        SecondLevelWarningn.value = SecondLevelWarning.value[0].real_time
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 三级预警
const ThreeLevelWarning = ref(null)
const ThreeLevelWarninh = ref(null)
const ThreeLevelWarningn = ref(null)
const getThreeLevelWarningData = async () => {
  try {
    const result = await getThreeLevelWarning()
    if (result && result.data && Array.isArray(result.data)) {
      ThreeLevelWarning.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据

      if (ThreeLevelWarning.value && ThreeLevelWarning.value[0]) {
        ThreeLevelWarninh.value = ThreeLevelWarning.value[0].history
        ThreeLevelWarningn.value = ThreeLevelWarning.value[0].real_time
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 四级预警
const FourLevelWarning = ref(null)
const FourLevelWarninh = ref(null)
const FourLevelWarningn = ref(null)
const getFourLevelWarningData = async () => {
  try {
    const result = await getFourLevelWarning()
    if (result && result.data && Array.isArray(result.data)) {
      FourLevelWarning.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据

      if (FourLevelWarning.value && FourLevelWarning.value[0]) {
        FourLevelWarninh.value = FourLevelWarning.value[0].history
        FourLevelWarningn.value = FourLevelWarning.value[0].real_time
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 区域概览
const regionData = ref(null)
const regionDataList = ref(null)
const current_flooded_points = ref(null)
const total_flood_prone_points = ref(null)
const forecast = ref(null)
const getRegionData = async () => {
  try {
    const result = await getRegion()

    if (result && result.data && Array.isArray(result.data)) {
      regionData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      regionDataList.value = regionData.value[0].districts
      current_flooded_points.value = regionData.value[0].current_flooded_points
      total_flood_prone_points.value = regionData.value[0].total_flood_prone_points
      forecast.value = regionData.value[0].forecast
      // console.log('111111111111111122222222222加载成功:', regionData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 协同处置
const disposeData = ref(null)
const disposeDataList = ref(null)
const getDisposeData = async () => {
  try {
    const result = await getDispose()

    if (result && result.data && Array.isArray(result.data)) {
      disposeData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      disposeDataList.value = disposeData.value[0].data
      // console.log('1111111111111111加载成功:', disposeData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 设备报警
const equipmentData = ref(null)
const equipmentDataList = ref(null)
const getEquipmentData = async () => {
  try {
    const result = await getEquipment()

    if (result && result.data && Array.isArray(result.data)) {
      equipmentData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      equipmentDataList.value = equipmentData.value[0].alerts

      // console.log('1111111111111111加载成功:', equipmentData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 舆情信息
const sentimentData = ref(null)
const sentimentDataList = ref([])
const getSentimentData = async () => {
  try {
    const result = await getSentiment()

    if (result && result.data && Array.isArray(result.data)) {
      sentimentData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      sentimentDataList.value = sentimentData.value[0].opinion_information

      // console.log('1111111111111111加载成功:', sentimentData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 历史推演
const historyData = ref(null)
const historyDataList = ref(null)
const getHistoryData = async () => {
  try {
    const result = await getHistory()

    if (result && result.data && Array.isArray(result.data)) {
      historyData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      historyDataList.value = historyData.value[0]
      // console.log('1111111111111111加载成功:', historyData.value)
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}

// 一级预警
const warningDataList = ref(null)
const getWarningData = async () => {
  try {
    const result = await getWarning()

    if (result && result.data && Array.isArray(result.data)) {
      warningDataList.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      // console.log('预警数据加载成功:', warningDataList.value)
      if (warningDataList.value && warningDataList.value[0] && warningDataList.value[0].districts) {
        // console.log('预警区域数据:', warningDataList.value[0].districts)
      } else {
        console.warn('预警区域数据不存在或格式不正确')
      }
    } else {
      console.error('获取预警数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
  }
}
// 防汛能力
const floodData = ref(null)
const floodpersonnel = ref(null)
const floodvehicle = ref(null)
const floodtime = ref(null)

const getFloodData = async () => {
  try {
    const result = await getflood()

    if (result && result.data && Array.isArray(result.data)) {
      floodData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      floodtime.value = floodData.value[0].time
      floodpersonnel.value = floodData.value[0].personnel
      floodvehicle.value = floodData.value[0].vehicles
      console.log('aaaaaaaaaaaaaaaa22222', floodpersonnel.value)

      // console.log('防汛数据11111111111111111111111:', floodData.value)
    } else {
      console.error('获取物资数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取物资数据失败:', error)
  }
}

// 应急物资
const goodsData = ref(null)
const goodsDataList = ref(null)
const goodsTotal = ref(null)

const getGoodsData = async () => {
  try {
    const result = await getGoods()

    if (result && result.data && Array.isArray(result.data)) {
      goodsData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据

      if (goodsData.value && goodsData.value[0]) {
        goodsTotal.value = goodsData.value[0].total

        // 处理物资数据，转换为我们需要的格式
        if (goodsData.value[0].departments && Array.isArray(goodsData.value[0].departments)) {
          goodsDataList.value = goodsData.value[0].departments
        } else {
          console.error('物资数据格式不正确:', goodsData.value[0])
          goodsDataList.value = []
        }
      }
    } else {
      console.error('获取物资数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取物资数据失败:', error)
  }
}

// 设备信息
const deviceData = ref(null)
const deviceDataList = ref(null)
const devictotalWarning = ref(null)
const devictime = ref(null)
const getdDeviceData = async () => {
  try {
    const result = await getDevice()

    if (result && result.data && Array.isArray(result.data)) {
      deviceData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      devictotalWarning.value = deviceData.value[0].totalWarning
      devictime.value = deviceData.value[0].time
      console.log(deviceData.value[0].data, '设备信息')

      deviceDataList.value = deviceData.value[0].data
    } else {
      console.error('获取事件数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取事件数据失败:', error)
  }
}

// 场景分类
const sceneData = ref(null)
const sceneDataList = ref(null)
const totalWarning = ref(null)
const totalOffline = ref(null)
const scenetime = ref(null)

const getSceneData = async () => {
  try {
    const result = await getScene()
    console.log(result, 'sceneDataList.value')
    if (result && result.data && Array.isArray(result.data)) {
      sceneData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      sceneDataList.value = sceneData.value[0].data
      totalWarning.value = sceneData.value[0].totalWarning
      totalOffline.value = sceneData.value[0].totalOffline
      scenetime.value = sceneData.value[0].time
    } else {
      console.error('获取事件数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取事件数据失败:', error)
  }
}

// 管网预警
const pipelineData = ref(null)
const pipelineDataTime = ref(null)
const getPipelineData = async () => {
  try {
    const result = await getPipeline()

    if (result && result.data && Array.isArray(result.data)) {
      pipelineData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
      pipelineDataTime.value = pipelineData.value[0].time
    } else {
      console.error('获取事件数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取事件数据失败:', error)
  }
}

// 事件累计发生次数
const eventCountData = ref(null)
const getEventCountData = async () => {
  try {
    const result = await getEventCount()
    // console.log('事件累计发生次数', result)

    if (result && result.data && Array.isArray(result.data)) {
      eventCountData.value = result.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析事件数据失败:', e)
            return null
          }
        })
        .filter(Boolean) // 过滤掉解析失败的数据
    } else {
      console.error('获取事件数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取事件数据失败:', error)
  }
}
const videoData = ref(null)
// Tab相关状态
const activeTab = ref('floodPoint') // 默认选中综治标签

// 切换标签
const switchTab = tab => {
  activeTab.value = tab
  // 切换标签后重新获取对应标签的视频数据
  getCameraListData()
}

// 添加onlineVideos变量
const totalVideos = ref(0)
const onlineVideos = ref(0)
const displayedVideos = ref(null)
// 获取监控视频
const getCameraListData = async () => {
  const result = await getCameraList({ pageSize: 15 }) // 先获取足够多的数据，再前端分页
  if (result.code === 200) {
    displayedVideos.value = result.rows
    totalVideos.value = result.total

    // 计算没有urlData的视频数量
    const noUrlDataCount = displayedVideos.value.filter(video => !video.urlData).length
    // 计算在线视频数量
    onlineVideos.value = totalVideos.value - noUrlDataCount

    console.log('getCameraListData-----------监控视频接口', result)
    // 使用nextTick确保DOM已更新后再初始化视频播放器
    nextTick(() => {
      displayedVideos.value.forEach((item, index) => {
        try {
          // 获取视频元素
          const videoElement = document.getElementById(`video-player-${index}`)
          if (!videoElement) {
            console.error(`无法找到视频元素 #video-player-${index}`)
            return
          }

          // 解析urlData字符串
          let videoUrl = ''
          if (item.urlData) {
            try {
              // 先解析外层JSON
              const urlDataObj = JSON.parse(item.urlData)
              // 再解析内层url字符串
              if (urlDataObj && urlDataObj.url) {
                const urlObj = JSON.parse(urlDataObj.url)
                // 获取flv格式的视频URL
                videoUrl = urlObj.hls
                console.log('🚀 ~ getCameraListData ~ videoUrl:', videoUrl)
              }
            } catch (error) {
              console.error(`解析视频URL数据失败:`, error)
            }
          }

          if (videoUrl) {
            const hls = new Hls()
            // 使用代理路径
            const proxyUrl = videoUrl
            hls.loadSource(proxyUrl)
            hls.attachMedia(videoElement)
            //hls.on(Hls.Events.MANIFEST_PARSED, () => video.play());
          } else {
            console.warn(`视频 ${index} 的URL为空`)
          }
        } catch (error) {
          console.error(`初始化视频播放器 ${index} 时出错:`, error)
        }
      })
    })
  }
}

// 获取assets静态资源
const getAssetsFile = url => {
  return new URL(`../../assets/images/home/<USER>
}

// 获取管网背景图
const getBackgroundImage = index => {
  const bgNumber = (index % 3) + 1
  return new URL(`../../assets/images/home/<USER>
}

// 根据索引获取对应的渐变色
const getWarningValueColor = (value, index) => {
  // 根据索引返回不同渐变色
  switch (index % 3) {
    case 0:
      return 'linear-gradient(180deg, #FFFFFF 0%, #76C8FC 100%)' // 蓝色渐变
    case 1:
      return 'linear-gradient(180deg, #FFFFFF 0%, #FF6A00 100%)' // 橙色渐变
    case 2:
      return 'linear-gradient(180deg, #FFFFFF 0%, #0A936B 100%)' // 绿色渐变
    default:
      return 'linear-gradient(180deg, #FFFFFF 0%, #76C8FC 100%)' // 默认蓝色渐变
  }
}
// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

let timer = null

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year} ${month} ${day}`
}

// 使用父组件传递的地图实例
// const api = ref(null)

// 图表相关
const barChartRef = ref(null)
let barChart = null
const verticalBarChartRef = ref(null)
let verticalBarChart = null
const pieChartRef = ref(null)
let pieChart = null
const verticalBarChart2Ref = ref(null)
let verticalBarChart2 = null
const footerChartRef = ref(null)
let footerChart = null
const rainfallChartRef = ref(null)
let rainfallChart = null
const dispatchChartRef = ref(null)
let dispatchChart = null

// 预警类型数据
const warningData = ref([
  { value: 30, name: '一级预警', itemStyle: { color: '#0098FA' } },
  { value: 25, name: '二级预警', itemStyle: { color: '#0CD9B5' } },
  { value: 20, name: '三级预警', itemStyle: { color: '#FDCC00' } },
  { value: 15, name: '四级预警', itemStyle: { color: '#DC79F5' } }
])
const initPieChart = () => {
  if (!pieChartRef.value) return

  // 确保容器已经渲染完成
  nextTick(() => {
    if (pieChart) {
      pieChart.dispose()
    }

    pieChart = echarts.init(pieChartRef.value)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: [
        {
          type: 'plain',
          orient: 'horizontal',
          bottom: '15%',
          left: 'center',
          itemWidth: 25,
          itemHeight: 14,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 16
          },
          formatter: name => name,
          selectedMode: false,
          padding: [0, 0, 0, 0],
          data: [
            { name: warningData.value[0].name, icon: 'rect' },
            { name: warningData.value[1].name, icon: 'rect' }
          ]
        },
        {
          type: 'plain',
          orient: 'horizontal',
          bottom: '5%',
          left: 'center',
          itemWidth: 25,
          itemHeight: 14,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 16
          },
          formatter: name => name,
          selectedMode: false,
          padding: [0, 0, 0, 0],
          data: [
            { name: warningData.value[2].name, icon: 'rect' },
            { name: warningData.value[3].name, icon: 'rect' }
          ]
        }
      ],
      series: [
        {
          name: '预警类型',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '40%'],
          avoidLabelOverlap: false,
          itemStyle: {
            // borderColor: '#fff',
            // borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          labelLine: {
            show: false
          },
          data: warningData.value
        }
      ]
    }

    pieChart.setOption(option)

    // 添加点击事件
    pieChart.on('click', params => {
      selectedWarningLevel.value = params.name
      loadSelectedWarningData()
    })
  })
}

// 标签数据
const tagsList = ref([
  {
    id: 1,
    name: '低洼地段',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 2,
    name: '人行下穿',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 3,
    name: '车辆下川',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 4,
    name: '泵站池',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 5,
    name: '排水泵',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 6,
    name: '雨污水泵站',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 7,
    name: '涉河入口',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 8,
    name: '雨水管网',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 9,
    name: '棚户区',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 10,
    name: '地下商超',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 11,
    name: '远年区域',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 12,
    name: '公共地下停车场',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  }
])
// 标签数据
const tagsList1 = ref([
  {
    id: 1,
    name: '低洼地段',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 2,
    name: '人行下穿',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 3,
    name: '车辆下川',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 4,
    name: '泵站池',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 5,
    name: '排水泵',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 6,
    name: '雨污水泵站',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 7,
    name: '涉河入口',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 8,
    name: '雨水管网',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 9,
    name: '棚户区',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 10,
    name: '地下商超',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  }
])

// 标签数据
const tagsList2 = ref([
  {
    id: 1,
    name: '低洼地段',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 2,
    name: '人行下穿',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 3,
    name: '车辆下川',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 4,
    name: '泵站池',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 5,
    name: '排水泵',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 6,
    name: '雨污水泵站',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 7,
    name: '涉河入口',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 8,
    name: '雨水管网',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 9,
    name: '棚户区',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 10,
    name: '地下商超',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 11,
    name: '远年区域',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  },
  {
    id: 12,
    name: '公共地下停车场',
    icon: 'https://picsum.photos/40/40',
    stats: '3/63/2'
  }
])

// 初始化柱状图改为初始化地图
const initBarChart = () => {
  if (!barChartRef.value) return

  barChart = echarts.init(barChartRef.value)

  // 注册地图数据
  echarts.registerMap('taiyuan', {
    type: 'FeatureCollection',
    features: taiyuanJson.map(item => {
      return {
        type: 'Feature',
        properties: { name: item.name },
        geometry: item.geometry
      }
    })
  })

  // 积水点数据 - 根据图片添加积水点
  const floodPoints = [
    { name: '尖草坪区', coord: [112.558851, 37.855804], value: '积水深度: 23cm', level: '三级预警' },
    { name: '万柏林区', coord: [112.487122, 37.939893], value: '积水深度: 23cm', level: '二级预警' },
    { name: '晋源区', coord: [112.564273, 37.817974], value: '积水深度: 2cm', level: '四级预警' },
    { name: '迎泽区', coord: [112.477849, 37.715619], value: '积水深度: 13mm', level: '四级预警' },
    { name: '小店区', coord: [112.564273, 37.817974], value: '积水深度: 20mm', level: '三级预警' },
    { name: '杏花岭区', coord: [112.560743, 37.839291], value: '积水深度: 23mm', level: '二级预警' }
  ]

  const option = {
    // backgroundColor: 'transparent',
    // tooltip: {
    //   trigger: 'item',
    //   formatter: function (data) {
    //     // 为积水点定制显示内容
    //     if (data.data && data.data.level) {
    //       return `${data.name}<br/>${data.data.value}<br/>${data.data.level}`
    //     }
    //     // 为区域显示内容
    //     if (!isNaN(data.value)) {
    //       return data.name + '：' + data.value
    //     } else {
    //       return data.name
    //     }
    //   }
    // },
    grid: {
      left: '20%',
      right: '0%',
      top: '5%',
      bottom: '5%'
    },
    series: [
      {
        type: 'map',
        map: 'taiyuan',
        name: '顶层地图',
        aspectScale: 1, // 长宽比
        left: '25%',
        top: 0,
        width: '50%',
        height: '90%',
        roam: false, // 禁止缩放和平移
        itemStyle: {
          normal: {
            areaColor: 'rgba(57,85,245,0.3)',
            borderColor: 'rgba(98,110,255,1)',
            borderWidth: 2,
            label: {
              show: true,
              color: '#fff'
            }
          }
        },
        emphasis: {
          label: {
            show: true,
            color: '#fff'
          },
          itemStyle: {
            areaColor: 'rgba(107,155,245,0.5)',
            borderColor: 'rgba(98,110,255,1)'
          }
        },
        data: regionDataList.value,
        // markPoint: {
        //   symbol: 'pin',
        //   symbolSize: 30,
        //   data: floodPoints,
        //   itemStyle: {
        //     color: function (params) {
        //       // 根据预警级别设置不同颜色
        //       const level = params.data.level
        //       if (level === '一级预警') return '#ff6b6b'
        //       if (level === '二级预警') return '#4ecdc4'
        //       if (level === '三级预警') return '#45b7d1'
        //       if (level === '四级预警') return '#96c93d'
        //       return '#ff6b6b' // 默认颜色
        //     }
        //   },
        //   label: {
        //     show: false
        //   },
        //   emphasis: {
        //     label: {
        //       show: true,
        //       formatter: '{b}'
        //     }
        //   }
        // },
        // 添加文本标注
        markLine: {
          symbol: 'none',
          lineStyle: {
            color: '#fff',
            type: 'solid'
          },
          data: [
            // 迎泽区
            [
              {
                coord: [112.72, 37.855804],
                symbol: 'none'
              },
              {
                coord: [112.87, 37.855804],
                symbol: 'none',
                label: {
                  show: true,
                  position: 'end',
                  formatter: regionDataList.value && regionDataList.value[0] ? regionDataList.value[0].value : '0',
                  color: '#fff',
                  fontSize: 22
                }
              }
            ],
            // 万柏林区
            [
              {
                coord: [112.327394, 37.860025],
                symbol: 'none'
              },
              {
                coord: [112.197394, 37.860025],
                symbol: 'none',
                label: {
                  show: true,
                  position: 'end',
                  formatter: regionDataList.value && regionDataList.value[1] ? regionDataList.value[1].value : '0',
                  color: '#fff',
                  fontSize: 22
                }
              }
            ],
            // 晋源区
            [
              {
                coord: [112.359192, 37.709163],
                symbol: 'none'
              },
              {
                coord: [112.20192, 37.709163],
                symbol: 'none',
                label: {
                  show: true,
                  position: 'end',
                  formatter: regionDataList.value && regionDataList.value[2] ? regionDataList.value[2].value : '0',
                  color: '#fff',
                  fontSize: 22
                }
              }
            ],
            // 小店区
            [
              {
                coord: [112.575235, 37.687974],
                symbol: 'none'
              },
              {
                coord: [112.775235, 37.687974],
                symbol: 'none',
                label: {
                  show: true,
                  position: 'end',
                  formatter: regionDataList.value && regionDataList.value[3] ? regionDataList.value[3].value : '0',
                  color: '#fff',
                  fontSize: 22
                }
              }
            ],
            // 杏花岭区
            [
              {
                coord: [112.75, 37.928113],
                symbol: 'none'
              },
              {
                coord: [112.9, 37.928113],
                symbol: 'none',
                label: {
                  show: true,
                  position: 'end',
                  formatter: regionDataList.value && regionDataList.value[4] ? regionDataList.value[4].value : '0',
                  color: '#fff',
                  fontSize: 22
                }
              }
            ],
            // 尖草坪区
            [
              {
                coord: [112.555322, 38.021398],
                symbol: 'none'
              },
              {
                coord: [112.755322, 38.021398],
                symbol: 'none',
                label: {
                  show: true,
                  position: 'end',
                  formatter: regionDataList.value && regionDataList.value[5] ? regionDataList.value[5].value : '0',
                  color: '#fff',
                  fontSize: 22
                }
              }
            ]
          ]
        },
        // 添加文本标注
        markText: {
          symbol: 'none',
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 22
          }
        },
        z: 1
      }
    ],
    // 添加图例
    visualMap: {
      min: 0,
      max: 600,
      left: 'left',
      top: 'bottom',
      text: ['高', '低'],
      calculable: true,
      inRange: {
        color: ['rgba(57,85,245,0.3)', 'rgba(107,155,245,0.5)']
      },
      textStyle: {
        color: '#fff'
      },
      show: false // 隐藏图例
    }
    // 添加标题
    // title: [
    //   {
    //     text: '累计易涝点\n{value|' + total_flood_prone_points.value + '}个',
    //     left: '6%',
    //     top: '10%',
    //     textStyle: {
    //       color: '#fff',
    //       fontSize: 26,
    //       lineHeight: 65,
    //       fontWeight: 'normal',
    //       rich: {
    //         value: {
    //           fontSize: 42,
    //           fontWeight: 'bold',
    //           padding: [0, 0, 0, 0]
    //         }
    //       }
    //     }
    //   },
    //   {
    //     text: '当前积涝点\n{value|' + forecast.value + '}个',
    //     left: '6%',
    //     top: '55%',
    //     fontWeight: 'normal',
    //     textStyle: {
    //       color: '#fff',
    //       fontSize: 26,
    //       lineHeight: 65,
    //       fontWeight: 'normal',
    //       rich: {
    //         value: {
    //           fontSize: 42,
    //           fontWeight: 'bold',
    //           padding: [0, 0, 0, 0]
    //         }
    //       }
    //     }
    //   }
    // ]
  }

  barChart.setOption(option)
}

// 初始化垂直柱状图
const initVerticalBarChart = () => {
  if (!verticalBarChartRef.value) return

  // Check if eventCountData is loaded
  if (!eventCountData.value || !eventCountData.value[0]) {
    // console.log('Event count data not loaded yet')
    return
  }

  verticalBarChart = echarts.init(verticalBarChartRef.value)

  // 从eventCountData中提取数据
  const xAxisData = eventCountData.value[0].districts.map(item => item.name || '未知区域')
  const historicalData = eventCountData.value[0].districts.map(item => item.historical_total || 0)
  const pendingData = eventCountData.value[0].districts.map(item => item.pending || 0)
  const unhandledData = eventCountData.value[0].districts.map(item => item.unprocessed || 0)
  const inProgressData = eventCountData.value[0].districts.map(item => item.in_progress || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      textStyle: {
        color: '#fff', // 图例文字颜色改为白色
        fontSize: 22
      },
      itemGap: 20 // 增加图例间距
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)', // 轴线颜色
            fontSize: 22
          }
        },
        axisLabel: {
          color: '#fff', // x轴标签文字颜色改为白色
          fontSize: 22
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)' // 轴线颜色
          }
        },
        axisLabel: {
          color: '#fff', // y轴标签文字颜色改为白色
          fontSize: 22
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)' // 分隔线颜色
          }
        }
      }
    ],
    series: [
      {
        name: '历史累计数',
        type: 'bar',
        color: 'rgba(75, 136, 208, 1)',
        emphasis: {
          focus: 'series'
        },
        data: historicalData
      },
      {
        name: '待处理',
        type: 'bar',
        color: 'rgba(105, 174, 113, 1)',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        },
        data: pendingData
      },
      {
        name: '未处理',
        color: 'rgba(133, 208, 216, 1)',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        },
        data: unhandledData
      },
      {
        name: '进行中',
        color: 'rgba(231, 212, 143, 1)',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series'
        },
        data: inProgressData
      }
    ]
  }

  verticalBarChart.setOption(option)
}

// 初始化底部对比图表
const initFooterChart = () => {
  if (!footerChartRef.value) return

  footerChart = echarts.init(footerChartRef.value)

  // 使用动态数据源 sjtjDatataList
  let xAxisData = ['天龙山区', '古交区', '小店区', '迎泽区', '万柏林区', '晋源区']
  let standardData = [100, 95, 85, 75, 70, 65] // 标准数据（深色柱子）
  let currentData = [18, 18, 18, 18, 18, 18] // 当前数据（浅色柱子）

  // 如果有动态数据，使用动态数据
  if (sjtjDatataList.value && sjtjDatataList.value.length > 0) {
    xAxisData = sjtjDatataList.value.map(item => item.name || '未知区域')
    currentData = sjtjDatataList.value.map(item => item.current || 0)
    // 标准数据可以根据实际需求调整，这里保持原有逻辑或设置为固定值
    standardData = sjtjDatataList.value.map(item => item.historical_total || 0)
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#4B88D0',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 20
      }
    },
    legend: {
      data: ['历史累计', '当前值'],
      textStyle: {
        color: '#fff',
        fontSize: 20
      },
      bottom: '0%',
      left: 'center',
      itemGap: 20,
      itemWidth: 40,
      itemHeight: 14
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '13%',
      top: '13%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 20,
        interval: 0,
        rotate: 0
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '(个)',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#fff',
          fontSize: 20,
          padding: [0, 50, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 20
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        max: 100
      },
      {
        type: 'value',
        name: '(个)',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#fff',
          fontSize: 20,
          padding: [0, 0, 0, 50]
        },
        position: 'right',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 20
        },
        splitLine: {
          show: false
        },
        max: 100
      }
    ],
    series: [
      {
        name: '历史累计',
        type: 'bar',
        data: standardData,
        barWidth: '30%',
        yAxisIndex: 0, // 绑定到左侧 y 轴（索引为0）
        itemStyle: {
          color: '#5A5A5A', // 深灰色，更接近图片
          borderRadius: [3, 3, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      {
        name: '当前值',
        type: 'bar',
        data: currentData,
        barWidth: '30%',
        yAxisIndex: 1, // 绑定到右侧 y 轴（索引为1）
        itemStyle: {
          color: '#C8956D', // 调整为更接近图片的棕色
          borderRadius: [3, 3, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 20,
          fontWeight: 'bold'
        }
      }
    ]
  }

  footerChart.setOption(option)
}

// 初始化垂直柱状图2
const initVerticalBarChart2 = () => {
  if (!verticalBarChart2Ref.value) return

  // Check if warningDataList is loaded
  if (!warningDataList.value || !warningDataList.value[0] || !warningDataList.value[0].districts) {
    console.log('Warning data districts not loaded yet')
    return
  }

  verticalBarChart2 = echarts.init(verticalBarChart2Ref.value)

  // 从warningDataList中提取数据
  const districts = warningDataList.value[0].districts || []
  const xAxisData = districts.map(item => item.name || '未知区域')
  const countData = districts.map(item => item.count || 0)

  const barWidth = 40
  const colors = []

  // 创建渐变色
  for (let i = 0; i < xAxisData.length; i++) {
    colors.push({
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#00A2EC' // 最左边
        },
        {
          offset: 0.5,
          color: '#098FCF' // 左边的右边 颜色
        },
        {
          offset: 0.5,
          color: '#00A2EC' // 右边的左边 颜色
        },
        {
          offset: 1,
          color: '#00A2EC'
        }
      ]
    })
  }

  const option = {
    grid: {
      left: '6%',
      right: '4%',
      top: '10%',
      bottom: '20%' // 增加底部间距，给x轴标签留出更多空间
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#A2A2A2',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        interval: 0,
        margin: 16 // 增加标签与轴线的距离
        // rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      // name: '积水深度(cm)',
      nameTextStyle: {
        color: '#fff',
        fontSize: 18,
        padding: [0, 30, 0, 0]
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2A2A2',
          type: 'dashed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        fontSize: 20
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#A2A2A2',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        barWidth: barWidth,
        data: countData,
        itemStyle: {
          normal: {
            color: function (params) {
              return colors[params.dataIndex % colors.length]
            }
          }
        },
        label: {
          show: true,
          position: 'center',
          color: '#fff',
          fontSize: 14,
          formatter: '{c}',
          offset: [3, 20]
        }
      },
      {
        z: 2,
        type: 'pictorialBar',
        data: countData,
        symbol: 'diamond',
        symbolOffset: [0, '50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          normal: {
            color: function (params) {
              return colors[params.dataIndex % colors.length]
            }
          }
        }
      },
      {
        z: 3,
        type: 'pictorialBar',
        symbolPosition: 'end',
        data: countData,
        symbol: 'diamond',
        symbolOffset: [0, '-50%'],
        symbolSize: [barWidth, barWidth * 0.5],
        itemStyle: {
          normal: {
            borderWidth: 0,
            color: function (params) {
              return colors[params.dataIndex % colors.length].colorStops[0].color
            }
          }
        }
      }
    ]
  }

  verticalBarChart2.setOption(option)
}

// 降雨日历图表
const initRainfallChart = () => {
  if (!rainfallChartRef.value) return

  nextTick(() => {
    if (rainfallChart) {
      rainfallChart.dispose()
    }
    rainfallChart = echarts.init(rainfallChartRef.value)

    // 从yltjDataList获取动态数据
    const chartData = yltjDataList.value || []
    const districtNames = chartData.map(item => item.name || '')
    const historicalRainfall = chartData.map(item => item.historical_total || 0)
    const currentRainfall = chartData.map(item => item.current || 0)

    // 计算Y轴最大值，确保数据能完整显示
    const maxHistorical = Math.max(...historicalRainfall, 0)
    const maxCurrent = Math.max(...currentRainfall, 0)
    const yAxisMax = Math.max(maxHistorical, maxCurrent, 100) * 1.2 // 增加20%的空间

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00d4ff',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        data: ['历史降雨', '当前雨量'],
        bottom: '0%',
        left: 'center',
        itemGap: 20,
        textStyle: {
          color: '#fff',
          fontSize: 20
        },
        itemWidth: 40,
        itemHeight: 14
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '12%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: districtNames,
        axisLine: {
          lineStyle: {
            color: '#4a90e2'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 20,
          interval: 0,
          rotate: 0
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'mm',
          nameLocation: 'end',
          nameTextStyle: {
            color: '#fff',
            fontSize: 20,
            padding: [0, 50, 0, 0]
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            fontSize: 20
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          },
          max: yAxisMax
        },
        {
          type: 'value',
          name: 'mm',
          nameLocation: 'end',
          nameTextStyle: {
            color: '#fff',
            fontSize: 20,
            padding: [0, 0, 0, 50]
          },
          position: 'right',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            fontSize: 20
          },
          splitLine: {
            show: false
          },
          max: yAxisMax
        }
      ],
      series: [
        {
          name: '历史降雨',
          type: 'bar',
          data: historicalRainfall,
          barWidth: '40%',
          yAxisIndex: 0, // 绑定到左侧 y 轴（索引为0）
          itemStyle: {
            color: '#5a7fc7',
            borderRadius: [2, 2, 0, 0]
          },
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 20
          }
        },
        {
          name: '当前雨量',
          type: 'bar',
          data: currentRainfall,
          barWidth: '40%',
          yAxisIndex: 1, // 绑定到右侧 y 轴（索引为1）
          itemStyle: {
            color: '#d4a574',
            borderRadius: [2, 2, 0, 0]
          },
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 20
          }
        }
      ]
    }

    rainfallChart.setOption(option)
  })
}

// 人员数据卡片
const personnelCards = ref([
  {
    id: 1,
    title: '流量人员数据',
    mainData: 220,
    type: 'gender',
    subData: [
      { label: '男', value: 182 },
      { label: '女', value: 12 }
    ]
  },
  {
    id: 2,
    title: '在线人员数据',
    mainData: 220,
    type: 'percentage',
    subData: 52
  },
  {
    id: 3,
    title: '应急队伍数据',
    mainData: 220,
    type: 'team',
    subData: [
      { label: '抢险队伍', value: 21 },
      { label: '其他队伍', value: 2 }
    ]
  }
])

// 预警列表数据
const warningList = ref([
  {
    location: '汾东大街唐槐路口',
    time: '2025-07-07',
    status: 'xxxx',
    depth: '26.00',
    area: 'xx'
  },
  {
    location: '汾东大街唐槐路口',
    time: '2025-07-07',
    status: 'xxxx',
    depth: '26.00',
    area: 'xx'
  },
  {
    location: '汾东大街唐槐路口',
    time: '2025-07-07',
    status: 'xxxx',
    depth: '26.00',
    area: 'xx'
  },
  {
    location: '汾东大街唐槐路口',
    time: '2025-07-07',
    status: 'xxxx',
    depth: '26.00',
    area: 'xx'
  }
])

// 人员调配信息数据
const personnelDeployment = ref([
  {
    name: '张三',
    role: '消防员',
    imageUrl: 'https://picsum.photos/100/100?random=1'
  },
  {
    name: '李四',
    role: '医护人员',
    imageUrl: 'https://picsum.photos/100/100?random=2'
  },
  {
    name: '王五',
    role: '救援队长',
    imageUrl: 'https://picsum.photos/100/100?random=3'
  }
])

// 物资信息数据
const suppliesInfo = ref([
  {
    name: '救生衣',
    count: 25,
    imageUrl: 'https://picsum.photos/100/100?random=4'
  },
  {
    name: '救生艇',
    count: 8,
    imageUrl: 'https://picsum.photos/100/100?random=5'
  },
  {
    name: '医疗箱',
    count: 15,
    imageUrl: 'https://picsum.photos/100/100?random=6'
  }
])

// 告警信息数据
const alertList = ref([
  {
    id: 1,
    content: '凌云东路与金安大道三段交汇处十字路口的路面积水仪积水深度已达到13.40cm。',
    time: '监测预警2025-04-21 16:53:09',
    status: '已核实'
  },
  {
    id: 2,
    content: '凌云东路与金安大道三段交汇处十字路口的路面积水仪积水深度已达到13.40cm。',
    time: '监测预警2025-04-21 16:53:09',
    status: '未核实'
  },
  {
    id: 3,
    content: '凌云东路与金安大道三段交汇处十字路口的路面积水仪积水深度已达到13.40cm。',
    time: '监测预警2025-04-21 16:53:09',
    status: '已核实'
  },
  {
    id: 4,
    content: '凌云东路与金安大道三段交汇处十字路口的路面积水仪积水深度已达到13.40cm。',
    time: '监测预警2025-04-21 16:53:09',
    status: '未核实'
  }
])

// topWarningList数据 - 用于显示预警数据
const topWarningList = ref([])

// 告警设备数据
const alarmDevices = ref([
  { value: 6, label: '流量计' },
  { value: 3, label: '雨量计' },
  { value: 5, label: '液位计' },
  { value: 2, label: '压力计' },
  { value: 8, label: '水位计' },
  { value: 4, label: '污水泵' },
  { value: 7, label: '排水泵' },
  { value: 1, label: '监控器' }
])

// 在script setup中添加rainfallData
const rainfallData = ref([
  {
    id: '01',
    area: '尖草坪区',
    personnel: 6,
    supplies: 56
  },
  {
    id: '02',
    area: '吉祥岭区',
    personnel: 8,
    supplies: 66
  },
  {
    id: '03',
    area: '小店区',
    personnel: 10,
    supplies: 75
  },
  {
    id: '04',
    area: '小店区',
    personnel: 10,
    supplies: 75
  }
])

// 泵站运行统计图表
const pumpRunningChartRef = ref(null)
let pumpRunningChart = null

// 泵站运行统计数据
// 移除静态数据，改为使用动态数据
// const pumpRunningData = ref([
//   { value: 206, name: '运行中', itemStyle: { color: '#4ecdc4' } },
//   { value: 159, name: '停运中', itemStyle: { color: '#314b7d' } },
//   { value: 100, name: '检修中', itemStyle: { color: '#b4b4b4' } }
// ])

// 初始化泵站运行统计图表
const initPumpRunningChart = () => {
  if (!pumpRunningChartRef.value) return

  nextTick(() => {
    if (pumpRunningChart) {
      pumpRunningChart.dispose()
    }

    pumpRunningChart = echarts.init(pumpRunningChartRef.value)

    // 使用动态数据 - 改为使用bzyxDatataList数据源
    const chartData = bzyxDatataList.value
      ? bzyxDatataList.value.map(item => {
          return {
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.name === '运行中' ? '#0CD9B5' : item.name === '维护中' ? '#FDCC00' : '#0E9CFF'
            }
          }
        })
      : []

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      // title: {
      //   text: '泵站运行统计',
      //   left: 'center',
      //   top: '5%',
      //   textStyle: {
      //     color: '#fff',
      //     fontSize: 16,
      //     fontWeight: 'normal'
      //   }
      // },
      series: [
        {
          name: '泵站运行统计',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['49%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            // borderColor: '#0c2456',
            // borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{c}',
            fontSize: 20,
            color: '#fff'
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            lineStyle: {
              color: '#8c8c8c'
            }
          },
          data: chartData
        }
      ]
    }

    pumpRunningChart.setOption(option)
  })
}

// 泵站运行时间统计图表
const pumpOperationChartRef = ref(null)
let pumpOperationChart = null

// 油耗水位图表
const oilWaterLevelChartRef = ref(null)
let oilWaterLevelChart = null

// 油耗量图表
const oilConsumptionChartRef = ref(null)
let oilConsumptionChart = null

// 泵站运行时间数据
const pumpOperationData = ref([
  { name: '泵站1', value: 46 },
  { name: '泵站2', value: 58 },
  { name: '泵站3', value: 72 },
  { name: '泵站4', value: 35 },
  { name: '泵站5', value: 80 },
  { name: '泵站6', value: 62 }
])

// 油耗水位数据
const oilWaterLevelData = ref([
  { name: '水位1', value: 55 },
  { name: '水位2', value: 65 },
  { name: '水位3', value: 75 },
  { name: '水位4', value: 40 },
  { name: '水位5', value: 85 },
  { name: '水位6', value: 70 }
])

// 油耗量数据
const oilConsumptionData = ref([
  { name: '油耗1', value: 50 },
  { name: '油耗2', value: 60 },
  { name: '油耗3', value: 70 },
  { name: '油耗4', value: 45 },
  { name: '油耗5', value: 80 },
  { name: '油耗6', value: 65 }
])

// 初始化泵站运行时间统计图表
const initPumpOperationChart = () => {
  if (!pumpOperationChartRef.value) return

  nextTick(() => {
    if (pumpOperationChart) {
      pumpOperationChart.dispose()
    }

    pumpOperationChart = echarts.init(pumpOperationChartRef.value)

    // 使用动态数据
    const chartData = pumpStation2ListData.value || []

    const barWidth = 35
    const colors = []

    // 创建渐变色
    for (let i = 0; i < chartData.length; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          { offset: 0, color: '#00A2EC' },
          { offset: 0.5, color: '#098FCF' },
          { offset: 0.5, color: '#00A2EC' },
          { offset: 1, color: '#00A2EC' }
        ]
      })
    }

    const option = {
      grid: {
        left: '10%',
        right: '4%',
        top: '10%',
        bottom: '20%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c} 小时'
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          interval: 0,
          margin: 16,
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(0, 4) + '..'
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '运行时长(小时)',
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
          padding: [0, 0, 10, 0]
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.map(item => item.value),
          type: 'bar',
          barWidth: barWidth,
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          },
          label: {
            show: true,
            position: 'center',
            color: '#fff',
            fontSize: 16,
            offset: [5, 20]
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colors[params.dataIndex % colors.length].colorStops[0].color
              }
            }
          }
        }
      ]
    }

    pumpOperationChart.setOption(option)
  })
}

// 初始化管网健康度图表
const initOilWaterLevelChart = () => {
  if (!oilWaterLevelChartRef.value) return

  nextTick(() => {
    if (oilWaterLevelChart) {
      oilWaterLevelChart.dispose()
    }

    oilWaterLevelChart = echarts.init(oilWaterLevelChartRef.value)

    // 使用管网健康度数据
    const chartData = pipeline_health.value || []

    const barWidth = 35
    const colors = []

    // 创建渐变色
    for (let i = 0; i < chartData.length; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          { offset: 0, color: '#00A2EC' },
          { offset: 0.5, color: '#098FCF' },
          { offset: 0.5, color: '#00A2EC' },
          { offset: 1, color: '#00A2EC' }
        ]
      })
    }

    const option = {
      grid: {
        left: '10%',
        right: '4%',
        top: '10%',
        bottom: '20%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}%'
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          interval: 0,
          margin: 16,
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(0, 4) + '..'
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '健康度(%)',
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
          padding: [0, 0, 10, 0]
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.map(item => item.value),
          type: 'bar',
          barWidth: barWidth,
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          },
          label: {
            show: true,
            position: 'center',
            color: '#fff',
            fontSize: 16,
            offset: [5, 20]
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colors[params.dataIndex % colors.length].colorStops[0].color
              }
            }
          }
        }
      ]
    }

    oilWaterLevelChart.setOption(option)
  })
}

// 初始化油耗量图表
const initOilConsumptionChart = () => {
  if (!oilConsumptionChartRef.value) return

  nextTick(() => {
    if (oilConsumptionChart) {
      oilConsumptionChart.dispose()
    }

    oilConsumptionChart = echarts.init(oilConsumptionChartRef.value)

    // 使用动态数据
    const chartData = pumpStation4ListData.value || []

    const barWidth = 35
    const colors = []

    // 创建渐变色
    for (let i = 0; i < chartData.length; i++) {
      colors.push({
        type: 'linear',
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          { offset: 0, color: '#00A2EC' },
          { offset: 0.5, color: '#098FCF' },
          { offset: 0.5, color: '#00A2EC' },
          { offset: 1, color: '#00A2EC' }
        ]
      })
    }

    const option = {
      grid: {
        left: '10%',
        right: '4%',
        top: '10%',
        bottom: '20%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c} L'
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          interval: 0,
          margin: 16,
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(0, 4) + '..'
            }
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '抽排量(L)',
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
          padding: [0, 0, 10, 0]
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.map(item => item.value),
          type: 'bar',
          barWidth: barWidth,
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          },
          label: {
            show: true,
            position: 'center',
            color: '#fff',
            fontSize: 16,
            offset: [5, 20]
          }
        },
        {
          z: 2,
          type: 'pictorialBar',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              color: function (params) {
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: chartData.map(item => item.value),
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [barWidth, barWidth * 0.5],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colors[params.dataIndex % colors.length].colorStops[0].color
              }
            }
          }
        }
      ]
    }

    oilConsumptionChart.setOption(option)
  })
}

// 初始化区域分布饼图
const distributionChartRef = ref(null)
let distributionChart = null

// 区域分布数据由pumpStation5ListData提供，不再需要静态数据

// 初始化区域分布图表
const initDistributionChart = () => {
  if (!distributionChartRef.value) return

  nextTick(() => {
    if (distributionChart) {
      distributionChart.dispose()
    }

    distributionChart = echarts.init(distributionChartRef.value)

    // 使用动态数据 pumpStation5ListData
    const chartData = pumpStation5ListData.value || []

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}'
      },
      // legend: {
      //   orient: 'vertical',
      //   right: '5%',
      //   top: 'center',
      //   itemWidth: 10,
      //   itemHeight: 10,
      //   itemGap: 15,
      //   formatter: name => {
      //     const data = chartData.find(item => item.name === name)
      //     return `${name}: ${data.value}`
      //   },
      //   textStyle: {
      //     color: '#fff',
      //     fontSize: 14
      //   }
      // },
      series: [
        {
          name: '区域分布',
          type: 'pie',
          left: '15%',
          radius: '55%',
          center: ['35%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            // borderColor: '#0c2456',
            // borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: function (params) {
              return '{b|' + params.name + '}\n{c|' + params.value + '}'
            },
            rich: {
              b: {
                color: '#fff',
                fontSize: 20,
                lineHeight: 20
              },
              c: {
                color: '#76C8FC',
                fontSize: 20,
                fontWeight: 'bold',
                lineHeight: 20
              }
            }
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 15,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)',
              width: 1
            }
          },
          color: ['#0E9CFF', '#DC79F5', '#FDCC00', '#0CD9B5', '#A081F3', '#FFA881', '#FF8080'],
          data: chartData
        }
      ]
    }

    distributionChart.setOption(option)
  })
}

// 声严程度分级图表
const severityChartRef = ref(null)
let severityChart = null

// 声严程度数据
const severityData = ref([
  { level: '一级', value: 10 },
  { level: '二级', value: 25 },
  { level: '三级', value: 30 },
  { level: '四级', value: 48 }
])

// 初始化声严程度分级图表
const initSeverityChart = () => {
  if (!severityChartRef.value) return

  nextTick(() => {
    if (severityChart) {
      severityChart.dispose()
    }

    severityChart = echarts.init(severityChartRef.value)

    // 使用动态数据 pumpStation6ListData，如果不可用则使用静态数据
    const chartData = pumpStation6ListData.value || severityData.value || []

    const option = {
      grid: {
        left: '10%',
        right: '4%',
        top: '10%',
        bottom: '15%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}'
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.level || item.name),
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14,
          interval: 0
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        name: '',
        nameTextStyle: {
          color: '#fff',
          fontSize: 14,
          padding: [0, 0, 10, 0]
        },

        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 14
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: chartData.map(item => {
            return {
              value: item.value,
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: '#5090D9' }, // 主颜色
                    { offset: 1, color: 'rgba(80, 144, 217, 1)' } // 浅色背景
                  ]
                },
                borderRadius: [4, 4, 0, 0] // 柱体上方圆角
              }
            }
          }),
          type: 'bar',
          barWidth: '40%',
          showBackground: true, // 显示背景
          backgroundStyle: {
            color: 'rgba(80, 144, 217, 0.3)', // 整体背景色，更浅
            borderRadius: [4, 4, 0, 0]
          },
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 14
          }
        }
      ]
    }

    severityChart.setOption(option)
  })
}

// 初始化调度统计图表
const initDispatchChart = () => {
  if (!dispatchChartRef.value) return

  nextTick(() => {
    if (dispatchChart) {
      dispatchChart.dispose()
    }
    dispatchChart = echarts.init(dispatchChartRef.value)

    // 使用动态数据或默认数据
    let chartData = []
    let categories = []

    if (ddzltjDataList.value && ddzltjDataList.value.length > 0) {
      // 使用动态数据
      categories = ddzltjDataList.value.map(item => item.type || '未知')
      chartData = ddzltjDataList.value.map(item => ({
        value: item.count || 0,
        itemStyle: { color: '#4a90e2' }
      }))
    } else {
      // 使用默认数据
      categories = ['语音', '视频', '短信', '预报推送']
      chartData = [
        { value: 100, itemStyle: { color: '#4a90e2' } },
        { value: 140, itemStyle: { color: '#4a90e2' } },
        { value: 130, itemStyle: { color: '#4a90e2' } },
        { value: 120, itemStyle: { color: '#4a90e2' } }
      ]
    }

    const option = {
      grid: {
        left: '5%',
        right: '5%',
        top: '15%',
        bottom: '25%'
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          color: '#ffffff',
          fontSize: 20,
          interval: 0,
          rotate: 0
        },
        axisLine: {
          lineStyle: {
            color: '#ffffff'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        // max: 160,
        axisLabel: {
          color: '#ffffff',
          fontSize: 20
        },
        axisLine: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [
        {
          data: chartData,
          type: 'bar',
          barWidth: '50%',
          label: {
            show: true,
            position: 'top',
            color: '#ffffff',
            fontSize: 20
          }
        }
      ]
    }

    dispatchChart.setOption(option)
  })
}

const weaherData = ref({})
// 组件挂载时启动定时器和初始化屏幕适配
onMounted(async () => {
  cstqGET().then(res => {
    console.log(res, '天气', JSON.parse(res.data[0].value))
    if (res.code === 200 && res.data.length) {
      weaherData.value = JSON.parse(res.data[0].value)
    }
  })
  yjcbdTable().then(res => {
    // console.log(res, '应急物资的仓库')
    if (res.code === 200) {
      warehouseOptions.value = res.rows
      console.log('应急物资仓库', warehouseOptions.value)
    }
  })
  // 先获取数据，然后再初始化图表
  await getYltjData()
  await getSjtjData()
  await getBzyxData()
  await getGwData()
  await getRywzData()
  await getDdzltjData()
  await getDdzlData()
  await getyldbjData()
  await getyldbjtbData()
  await getpsssData()
  await getPssstbData()
  await getsghxData()
  // await getPipelineData()
  // await getSceneData()
  // await getdDeviceData()
  // await getGoodsData()
  // await getFloodData()
  // await getWarningData()
  // await getHistoryData()
  // 舆情信息
  await getSentimentData()
  // await getEquipmentData()
  await getDisposeData()
  // 区域概览
  await getRegionData()
  // await getFirstLevelWarningData()
  // await getSecondLevelWarningData()
  // await getThreeLevelWarningData()
  // await getFourLevelWarningData()
  // await getEmergencySuppliesData()
  // await getPumpStation1Data()
  // await getPumpStation2Data()
  // await getPumpStation3Data()
  // 事件累计发生次数
  await getEventCountData()
  // await getPumpStation4Data()
  // await getPumpStation5Data()
  // await getPumpStation6Data()
  // await getImpactofwaterloggingFc()
  // await getImpactofwaterlogging1Fc()
  // await getImpactofwaterlogging2Fc()

  // 默认加载一级预警的实时数据
  //loadSelectedWarningData()

  // 更新时间
  updateTime() // 立即执行一次
  timer = setInterval(updateTime, 1000) // 每秒更新一次

  // 确保DOM渲染完成后再获取摄像头数据并初始化视频播放器
  nextTick(async () => {
    await getCameraListData()
  })

  // 初始化图表
  nextTick(() => {
    initBarChart()
    initVerticalBarChart()
    initPumpRunningChart()
    initPumpOperationChart() // 添加泵站运行时间统计图表初始化
    initOilWaterLevelChart() // 添加油耗水位图表初始化
    initOilConsumptionChart() // 添加油耗量图表初始化
    initDistributionChart() // 添加初始化区域分布图表
    initPieChart()
    initFooterChart() // 添加底部对比图表初始化
  })
  initVerticalBarChart2()
  initSeverityChart() // 添加声严程度分级图表初始化
  initRainfallChart() // 添加降雨日历图表初始化
  initDispatchChart() // 添加调度统计图表初始化

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    barChart && barChart.resize()
    verticalBarChart && verticalBarChart.resize()
    pieChart && pieChart.resize()
    pumpRunningChart && pumpRunningChart.resize()
    pumpOperationChart && pumpOperationChart.resize() // 添加泵站运行时间图表响应式
    footerChart && footerChart.resize() // 添加底部图表响应式
    oilWaterLevelChart && oilWaterLevelChart.resize() // 添加油耗水位图表响应式
    oilConsumptionChart && oilConsumptionChart.resize() // 添加油耗量图表响应式
    distributionChart && distributionChart.resize() // 添加区域分布图表响应式
    verticalBarChart2 && verticalBarChart2.resize()
    severityChart && severityChart.resize() // 添加声严程度分级图表响应式
    rainfallChart && rainfallChart.resize() // 添加降雨日历图表响应式
    dispatchChart && dispatchChart.resize() // 添加调度统计图表响应式
  })

  // 地图联动功能已移除

  // 使用父组件传递的地图实例
  // try {
  //   // 使用父组件的地图实例
  //   if (props.mapApi) {
  //     api.value = props.mapApi
  //     console.log('使用父组件的地图实例成功')
  //   } else {
  //     console.error('未接收到父组件的地图实例')
  //   }
  // } catch (error) {
  //   console.error('使用地图实例失败:', error)
  // }
})

// 监听yltjDataList变化，重新渲染降雨图表
watch(
  yltjDataList,
  newData => {
    if (newData && newData.length > 0) {
      nextTick(() => {
        initRainfallChart()
      })
    }
  },
  { deep: true }
)

// 监听ddzltjDataList变化，重新渲染调度统计图表
watch(
  ddzltjDataList,
  newData => {
    if (newData && newData.length > 0) {
      nextTick(() => {
        initDispatchChart()
      })
    }
  },
  { deep: true }
)

// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 销毁图表实例
  if (barChart) {
    barChart.dispose()
    barChart = null
  }
  if (verticalBarChart) {
    verticalBarChart.dispose()
    verticalBarChart = null
  }
  if (pieChart) {
    pieChart.dispose()
    pieChart = null
  }
  if (pumpRunningChart) {
    pumpRunningChart.dispose() // 销毁泵站图表实例
    pumpRunningChart = null
  }
  if (oilWaterLevelChart) {
    oilWaterLevelChart.dispose() // 销毁油耗水位图表实例
    oilWaterLevelChart = null
  }
  if (oilConsumptionChart) {
    oilConsumptionChart.dispose() // 销毁油耗量图表实例
    oilConsumptionChart = null
  }
  if (verticalBarChart2) {
    verticalBarChart2.dispose()
    verticalBarChart2 = null
  }
  if (severityChart) {
    severityChart.dispose() // 销毁声严程度分级图表实例
    severityChart = null
  }

  if (distributionChart) {
    distributionChart.dispose() // 销毁区域分布图表实例
    distributionChart = null
  }

  // 销毁DTS实例
  // if (api.value) {
  //   api.value = null
  // }

  if (pumpOperationChart) {
    pumpOperationChart.dispose() // 销毁泵站运行时间图表实例
    pumpOperationChart = null
  }
  if (footerChart) {
    footerChart.dispose() // 销毁底部图表实例
    footerChart = null
  }
  if (dispatchChart) {
    dispatchChart.dispose() // 销毁调度统计图表实例
    dispatchChart = null
  }
})

// 监听warningDataList的变化，更新图表
watch(
  () => warningDataList.value,
  newVal => {
    if (newVal && newVal[0] && newVal[0].districts) {
      nextTick(() => {
        initVerticalBarChart2()
      })
    }
  },
  { deep: true }
)

// 监听bzyxDatataList的变化，更新泵站运行统计图表
watch(
  () => bzyxDatataList.value,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initPumpRunningChart()
      })
    }
  },
  { deep: true }
)

// 监听pumpStation2ListData的变化，更新泵站运行时间统计图表
watch(
  () => pumpStation2ListData.value,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initPumpOperationChart()
      })
    }
  },
  { deep: true }
)

// 监听pipeline_health的变化，更新管网健康度图表
watch(
  () => pipeline_health.value,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initOilWaterLevelChart()
      })
    }
  },
  { deep: true }
)

// 监听pumpStation4ListData的变化，更新抽排量图表
watch(
  () => pumpStation4ListData.value,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initOilConsumptionChart()
      })
    }
  },
  { deep: true }
)

// 监听pumpStation5ListData的变化，更新易涝点统计图表
watch(
  () => pumpStation5ListData.value,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initDistributionChart()
      })
    }
  },
  { deep: true }
)

// 监听pumpStation6ListData的变化，更新预警信息声严程度分级图表
watch(
  () => pumpStation6ListData.value,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initSeverityChart()
      })
    }
  },
  { deep: true }
)

const emergencySupplies = ref([
  { num: '2011', unit: '副', name: '手套' },
  { num: '7', unit: '台', name: '编织袋' },
  { num: '20', unit: '条', name: '麻袋' },
  { num: '20', unit: '条', name: '皮刻锹' },
  { num: '6', unit: '吨', name: '铅丝' },
  { num: '9', unit: '套', name: '石笼网' },
  { num: '8', unit: '卷', name: '油毡' },
  { num: '8', unit: '个', name: '发电机' },
  { num: '23', unit: '顶', name: '帐篷' },
  { num: '9', unit: '套', name: '石笼网' },
  { num: '8', unit: '卷', name: '油毡' },
  { num: '8', unit: '个', name: '发电机' }
])
const yuntuClick = () => {
  emit('yuntuClickShow', 1)
}

// 舆情信息弹窗相关
const sentimentDialogVisible = ref(false)
const sentimentDateValue = ref('')
const currentSentimentStatus = ref('全部')
// const sentimentStatusOptions = ref([
//   { label: '全部', value: 'all' },
//   { label: '已核实', value: '已核实' },
//   { label: '未核实', value: '未核实' },
//   { label: '处理中', value: '处理中' }
// ])
const sentimentColumns = ref([
  { label: '标题', prop: 'title' },
  { label: '内容', prop: 'content' },
  { label: '状态', prop: 'handleStatusName' }
])

const sentimentTableData = ref([])
const sentimentTotal = ref(0)
const sentimentPageNum = ref(1)
const sentimentPageSize = ref(20)
const formInlineSentiment = ref({
  area: '',
  status: ''
})
const sentimentOptions = ref([
  { name: '尖草坪区', id: 1 },
  { name: '杏花岭区', id: 2 },
  { name: '小店区', id: 3 },
  { name: '迎泽区', id: 4 },
  { name: '万柏林区', id: 5 },
  { name: '晋源区', id: 6 }
])
const sentimentStatusOptions = ref([
  { name: '已核实', value: '已核实' },
  { name: '未核实', value: '未核实' },
  { name: '处理中', value: '处理中' }
])

// 调度指令表格数据
const dispatchTableData = ref([
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 1,
    method: '语音'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 2,
    method: '视频'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 3,
    method: '平台推送'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 3,
    method: '平台推送'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 3,
    method: '平台推送'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 3,
    method: '平台推送'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 3,
    method: '平台推送'
  },
  {
    department: '排管中心',
    time: '2025-03-12 12:00',
    target: 'xxx',
    count: 2,
    method: '短信'
  },
  {
    department: '水务局',
    time: '2025-03-12 13:00',
    target: 'yyy',
    count: 1,
    method: '语音'
  },
  {
    department: '应急管理局',
    time: '2025-03-12 14:00',
    target: 'zzz',
    count: 4,
    method: '视频'
  },
  {
    department: '城管局',
    time: '2025-03-12 15:00',
    target: 'aaa',
    count: 2,
    method: '平台推送'
  },
  {
    department: '交通局',
    time: '2025-03-12 16:00',
    target: 'bbb',
    count: 3,
    method: '短信'
  }
])

// 查询舆情信息
const onSubmitSentiment = () => {
  console.log('查询条件:', formInlineSentiment.value)
  sentimentPageNum.value = 1
  loadSentimentData()
}

// 关闭舆情信息弹窗
const handleSentimentDialogClose = () => {
  sentimentDialogVisible.value = false
}

// 舆情信息筛选条件变更
const handleSentimentSearch = () => {
  sentimentPageNum.value = 1 // 重置为第一页
  loadSentimentData()
}

// 舆情信息重置按钮
const handleSentimentReset = () => {
  formInlineSentiment.value.area = ''
  formInlineSentiment.value.status = ''
  sentimentDateValue.value = ''
  currentSentimentStatus.value = '全部'
  sentimentPageNum.value = 1 // 重置为第一页
  loadSentimentData()
}

// 仓库下拉框选择处理
const handleSentimentStatusCommand = command => {
  currentSentimentStatus.value = command
}

// 分页大小变化处理
const handleSentimentSizeChange = size => {
  sentimentPageSize.value = size
  console.log('分页大小变化处理', size)
  sentimentPageNum.value = 1 // 切换每页大小时重置为第一页
  loadSentimentData()
}

// 当前页变化处理
const handleSentimentCurrentChange = page => {
  sentimentPageNum.value = page
  loadSentimentData()
}

// 加载舆情信息表格数据
const loadSentimentData = async () => {
  console.log('获取舆情信息数据')

  // 构建查询参数
  const params = {
    pageNum: sentimentPageNum.value,
    pageSize: sentimentPageSize.value
  }

  // 添加查询条件
  if (formInlineSentiment.value.area) {
    params.area = formInlineSentiment.value.area
  }
  if (formInlineSentiment.value.status) {
    params.status = formInlineSentiment.value.status
  }

  if (currentSentimentStatus.value && currentSentimentStatus.value !== 'all') {
    params.status = currentSentimentStatus.value
  }

  if (sentimentDateValue.value) {
    params.date = sentimentDateValue.value
  }

  try {
    // 尝试调用API获取数据
    const res = await getSentimentTable(params)

    if (res.code === 200 && res.rows) {
      sentimentTableData.value = res.rows
      sentimentTotal.value = res.total || res.rows.length
      console.log('舆情信息数据总数:', sentimentTotal.value)
    } else if (res.data && Array.isArray(res.data)) {
      // 处理原始的舆情数据格式
      const sentimentData = res.data
        .map(item => {
          try {
            return JSON.parse(item.value)
          } catch (e) {
            console.error('解析舆情数据失败:', e)
            return null
          }
        })
        .filter(Boolean)

      if (sentimentData) {
        // 分页处理
        const startIndex = (sentimentPageNum.value - 1) * sentimentPageSize.value
        const endIndex = startIndex + sentimentPageSize.value

        sentimentTableData.value = filteredData.slice(startIndex, endIndex)
        sentimentTotal.value = filteredData.length
      } else {
        // 使用模拟数据
        useMockSentimentData()
      }
    } else {
      // 使用模拟数据
      useMockSentimentData()
    }

    console.log('舆情信息数据', sentimentTableData.value)
  } catch (e) {
    console.log('获取舆情信息数据失败', e)
    // 发生错误时使用模拟数据
    useMockSentimentData()
  }
}

// 使用模拟舆情数据
const useMockSentimentData = () => {
  const mockData = [
    {
      title: '太原市迎泽区某路段积水严重，市民出行受阻',
      time: '2025-07-07 08:30',
      status: '已核实',
      source: '新浪微博'
    },
    {
      title: '暴雨致使万柏林区地下通道积水，交通部门紧急处理',
      time: '2025-07-07 09:15',
      status: '处理中',
      source: '今日头条'
    },
    { title: '杏花岭区某小区地下车库被淹，多辆车辆受损', time: '2025-07-07 10:20', status: '已核实', source: '抖音' },
    { title: '尖草坪区积水点增多，防汛部门连夜排水', time: '2025-07-07 11:30', status: '未核实', source: '微信' },
    {
      title: '晋源区某工地因暴雨导致安全隐患，已紧急停工',
      time: '2025-07-07 12:45',
      status: '已核实',
      source: '新闻媒体'
    },
    { title: '小店区商场屋顶漏水，顾客紧急疏散', time: '2025-07-07 13:50', status: '处理中', source: '抖音' },
    {
      title: '太原市防汛应急响应提升至二级，各部门全力应对',
      time: '2025-07-07 14:25',
      status: '已核实',
      source: '新浪微博'
    },
    {
      title: '市民反映部分地区排水不畅，相关部门已介入调查',
      time: '2025-07-07 15:10',
      status: '未核实',
      source: '今日头条'
    },
    {
      title: '太原市气象局发布暴雨黄色预警，提醒市民注意安全',
      time: '2025-07-07 16:05',
      status: '已核实',
      source: '微信'
    },
    {
      title: '多部门联合开展防汛演练，提高应急处置能力',
      time: '2025-07-07 17:20',
      status: '已核实',
      source: '新闻媒体'
    }
  ]

  // 根据筛选条件过滤数据
  let filteredData = [...mockData]

  if (currentSentimentStatus.value && currentSentimentStatus.value !== 'all') {
    filteredData = filteredData.filter(item => item.status === currentSentimentStatus.value)
  }

  if (sentimentDateValue.value) {
    const filterDate = sentimentDateValue.value
    filteredData = filteredData.filter(item => item.time && item.time.includes(filterDate))
  }

  // 分页处理
  const startIndex = (sentimentPageNum.value - 1) * sentimentPageSize.value
  const endIndex = startIndex + sentimentPageSize.value

  sentimentTableData.value = filteredData.slice(startIndex, endIndex)
  sentimentTotal.value = filteredData.length
}

// 舆情信息
const openSentimentDialog = () => {
  sentimentDialogVisible.value = true
  loadSentimentData() // 首次打开弹窗加载数据
}
</script>

<style lang="scss" scoped>
// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  pointer-events: none; // 设置为none，让鼠标事件穿透到地图
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

// .bg-layer-3 {
// background-image: url('@/assets/images/home/<USER>');
// z-index: 3;
// }

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: auto;
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.left-container {
  left: 0;
  width: 32%;
  padding: vh(40) vw(0) vh(60) vw(100);
  // background: blue;
  .left-section {
    width: 100%;
    height: 100%;
    // padding: vh(40) vw(0) vh(60) vw(100);
    pointer-events: auto; // 确保可以与左侧内容交互

    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .time-weather {
    display: flex;
    align-items: center;
    color: #fff;
    padding-left: vw(40);
    // background: blue;
    .time-date {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .time {
      font-size: vh(24);
    }

    .date {
      font-size: vh(18);
      margin-right: vw(2);
    }

    .divider {
      margin: 0 vw(20);
      width: vw(1);
      height: vh(30);
      background-color: #3a607c;
    }

    .weather {
      display: flex;
      align-items: center;
    }

    .weather-icon {
      width: vw(24);
      height: vh(24);
      margin-right: vw(10);
    }

    .weather-icon1 {
      width: vw(24);
      height: vh(22);
    }

    .temperature {
      font-size: vh(24);
    }
  }
  .content-layout-first {
    width: 100%;
    // background: rgba(0, 0, 0, 0.2);
    .content-layout-header {
      width: 100%;
      height: vh(140);
      background-image: url('@/assets/images/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 vw(95);

      .weather-info-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        color: #fff;
        padding: 0 vw(15);
      }

      .current-weather {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        gap: vw(48);
      }

      .weather-label {
        font-weight: normal;
        font-size: vh(24);
        color: #ffffff;
        line-height: vh(33);
      }

      .city-info {
        display: flex;
        align-items: center;
      }

      .location-icon {
        display: inline-block;
        width: vw(22);
        height: vh(24);
        margin-right: vw(20);
      }

      .city-name {
        font-weight: normal;
        font-size: vh(32);
        color: #ffffff;
        margin-top: vh(-8);
        display: flex;
        align-items: center;
      }

      .weather-detail {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        gap: vw(15);
        min-width: 400px;
        font-size: 23px;
      }

      .weather-icon-large {
        display: flex;
        align-items: center;
      }

      .weather-icon-large img {
        width: vw(24);
        height: vh(24);
        margin-right: vw(10);
        object-fit: contain;
      }

      .weather-data {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .weather-type {
        display: flex;
        align-items: center;
        gap: vw(10);
      }

      .weather-name {
        font-weight: normal;
        font-size: vh(30);
        color: #ffffff;
        line-height: vh(42);
        text-align: center;
        font-style: normal;
      }

      .weather-temp {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        line-height: vh(30);
        font-style: normal;
        text-align: center;
        width: vw(72);
        height: vh(31);
        background: #1ecfa5;
        border-radius: vw(16);
      }

      .wind-info {
        display: flex;
        align-items: center;
        gap: vw(20);
        font-weight: normal;
        font-size: vh(27);
        color: #ffffff;
        text-align: left;
        font-style: normal;
        margin-top: vh(3);
      }

      .weather-forecast {
        display: flex;
        height: 100%;
        align-items: center;
      }

      .forecast-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 vw(30);
      }

      .forecast-title {
        font-weight: normal;
        font-size: vh(20);
        color: #ffffff;
        text-align: left;
        font-style: normal;
      }

      .forecast-value {
        .forecast-value-num {
          font-weight: bold;
          font-size: vh(32);
          color: #24ceb8;
          font-style: normal;
        }

        .forecast-value-unit {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }
      }

      .forecast-value1 {
        margin-top: 10px;
        .forecast-value-num {
          font-weight: bold;
          font-size: vh(24);
          color: #92d3ec;
          font-style: normal;
        }

        .forecast-value-unit {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }
      }

      .publish-info {
        width: vw(195);
        height: vh(77);
        background: rgba(110, 204, 255, 0.04);
        border-radius: vw(1);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        gap: vw(20);
      }

      .publish-label {
        font-weight: normal;
        font-size: vh(25);
        color: #ffffff;
        font-style: normal;
      }

      .publish-times {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        font-style: normal;
      }

      .time-item {
        font-weight: normal;
        font-size: vh(22);
        color: #ffffff;
        font-style: normal;
      }

      .divider-line {
        width: vw(1);
        height: vh(70);
      }
    }
  }
  .left-content {
    width: 100%;
    height: calc(100% - vh(180));
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1%;

    .content-layout-second {
      height: 100%;
      width: 35%;
      border-radius: vh(4);
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      gap: vw(20);
      .second-section-top {
        width: 100%;
        height: 30%;
        display: flex;
        flex-direction: column;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);

        .rainfall-title-area {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          width: 100%;
          height: vh(45);
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;
          padding: 0 vw(45);

          .rainfall-title-left {
            display: flex;
            align-items: center;
            gap: vw(20);

            .rainfall-main-title {
              font-size: vh(24);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }
          }
        }

        .rainfall-chart-area {
          flex: 1;
          padding: vh(10) vw(10) vh(10) vw(10);
          display: flex;
          align-items: stretch;
          gap: vw(10);

          .rainfall-chart {
            flex: 1;
            height: 100%;
          }

          .rainfall-title-right {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: vw(150);

            .rainfall-stats {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: vh(20);

              .rainfall-stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: vh(5);

                .rainfall-stat-label {
                  font-size: vh(24);
                  color: #ffffff;
                  opacity: 0.8;
                  line-height: 1;
                }

                .rainfall-stat-value {
                  font-size: vh(24);
                  color: #00d4ff;
                  font-weight: bold;
                  line-height: 1;

                  &.current {
                    color: #ff8c42;
                  }
                }
              }
            }
          }
        }
      }

      .second-section-left {
        width: 100%;
        height: 40%;
        display: flex;
        flex-direction: column;
        gap: vh(20);
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);

        .title-area {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: vh(45);
          // padding: 0 vh(20);
          background-image: url('@/assets/images/home/<USER>');
          // background: red;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;

          padding: 0 vw(45);

          .title-left {
            display: flex;
            align-items: center;
            gap: vw(20);

            .main-title {
              font-size: vh(24);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }

            .sub-title {
              font-size: vh(22);
              color: #6eccff;
            }
          }

          .title-right {
            .update-time {
              font-size: vh(20);
              color: #d8f1ff;
            }
          }
        }

        .chart-area {
          flex: 1;
          width: 100%;
          display: flex;
          justify-content: space-between;

          align-items: center;

          .chart-item-container {
            width: 50%;
            height: 100%;

            .venn-diagram {
              position: relative;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              // padding: 50px 30px;

              .circle {
                position: absolute;
                width: 200px;
                height: 200px;
                border-radius: 50%;
                // background: transparent;

                &.circle-1 {
                  top: 20px;
                  left: 50%;
                  transform: translateX(-50%);
                  z-index: 1;
                  border: 15px solid #0fabff;
                }

                &.circle-2 {
                  bottom: 40px;
                  left: 70px;
                  z-index: 2;
                  border: 15px solid #0deec7;
                }

                &.circle-3 {
                  bottom: 40px;
                  right: 70px;
                  z-index: 3;
                  border: 15px solid #ffe000;
                }
              }
              .circle:hover {
                scale: 1.1;
                z-index: 5;
                cursor: pointer;
              }

              .circle-label {
                position: absolute;
                color: #ffffff;
                font-size: 20px;
                text-align: center;
                white-space: nowrap;
                font-weight: 400;
                line-height: 1.2;

                &.circle-label-1 {
                  top: -45px;
                  left: 20px;
                  width: 10px;
                  text-align: center;
                }

                &.circle-label-2 {
                  bottom: -40px;
                  left: -40px;
                  width: 120px;
                  text-align: center;
                }

                &.circle-label-3 {
                  bottom: -40px;
                  left: 40px;
                  width: 120px;
                  text-align: center;
                }

                .label-text {
                  font-weight: 400;
                  margin-bottom: 1px;
                  line-height: 1.2;
                }

                .label-unit {
                  font-size: 10px;
                  opacity: 0.9;
                }
              }

              .intersection {
                position: absolute;
                color: #ffffff;
                font-size: 20px;
                font-weight: bold;
                z-index: 10;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);

                &.intersection-12 {
                  top: 225px;
                  left: 50%;
                  transform: translateX(-50%);
                }

                &.intersection-13 {
                  bottom: 52%;
                  left: 38%;
                }

                &.intersection-23 {
                  bottom: 52%;
                  right: 38%;
                }

                &.intersection-123 {
                  top: 55%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                }
              }
            }
          }

          .bar-chart {
            width: 50%;
            height: 100%;
          }
        }
      }
      .second-section-footer {
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        width: 100%;
        height: 30%;
        display: flex;
        flex-direction: column;

        .title-area {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 20px;

          .title-left {
            .main-title {
              color: #fff;
              font-size: 18px;
              font-weight: 500;
            }
          }
        }

        .chart-area {
          flex: 1;
          padding: 10px 10px;
          min-height: 200px;

          .footer-chart {
            width: 100%;
            height: 100%;
            min-height: 200px;
          }
        }
      }
    }

    .content-layout-third {
      height: 100%;
      width: 65%;
      background: linear-gradient(90deg, rgba(6, 72, 146, 0.24) 0%, rgba(6, 79, 156, 0.06) 100%);
      border-radius: vh(4);
      display: flex; // 添加flex布局
      justify-content: space-between;
      align-items: center;
      flex-direction: column;
      gap: vw(20); // 添加间距
      .second-section-right {
        width: 100%;
        height: 35%;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        gap: vh(10);

        // 标题区域样式
        .title-area {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: vh(45);
          padding: 0 vw(45);
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;

          .title-left {
            display: flex;
            align-items: center;

            .main-title {
              font-size: vh(24);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }
          }
        }
        .chart-item-container {
          width: 100%;
          height: calc(100% - vh(55));
          display: flex;
          justify-content: space-between;
          align-items: center;
          .chart-item-1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 50%;
            height: 100%;
            border-radius: vh(4);
            .chart-item-1-1 {
              width: 60%;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              // background: rgba(6, 72, 146, 0.35);
              border-radius: vh(4);
              .chart-item-title {
                font-size: vh(22);
                color: #ffffff;
                font-family: JiangChengXieHei;
                font-weight: normal;
                margin-bottom: vh(5);
                background-image: url('@/assets/images/home/<USER>');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: center;
                height: vh(30);
                width: 100%;
                padding: 0 vw(30);
              }

              .pump-running-chart {
                width: 100%;
                height: calc(100% - vh(40));
                padding: vh(10) vw(10);
              }
            }
            .chart-item-1-2 {
              width: 40%;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              // background: rgba(6, 72, 146, 0.35);
              border-radius: vh(4);
              padding: vh(20);

              .pump-station-stats {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                align-items: center;

                .stats-row {
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: vh(20);

                  .stat-item {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    padding: 0 vw(10);

                    .stat-label {
                      font-size: vh(16);
                      color: #ffffff;
                      font-family: JiangChengXieHei;
                      margin-bottom: vh(10);
                      line-height: 1.2;
                      opacity: 0.8;
                    }

                    .stat-value {
                      font-size: vh(28);
                      color: #00d4ff;
                      font-family: JiangChengXieHei;
                      font-weight: bold;

                      .stat-unit {
                        font-size: vh(18);
                        color: #ffffff;
                        margin-left: vw(5);
                        opacity: 0.8;
                      }
                    }
                  }
                }
              }
            }
          }
          .chart-item-2 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 50%;
            height: 100%;
            border-radius: vh(4);
            .chart-item-2-1 {
              height: 100%;
              width: 60%;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              // background: rgba(6, 72, 146, 0.35);
              border-radius: vh(4);
              .chart-item-title {
                font-size: vh(22);
                color: #ffffff;
                font-family: JiangChengXieHei;
                font-weight: normal;
                margin-bottom: vh(5);
                background-image: url('@/assets/images/home/<USER>');
                background-size: cover;
                background-repeat: no-repeat;
                background-position: center;
                height: vh(30);
                width: 100%;
                padding: 0 vw(30);
              }
              .oil-water-level-chart {
                width: 100%;
                height: calc(100% - vh(40));
                padding: vh(10) vw(10);
              }
            }
            .chart-item-2-2 {
              height: 100%;
              width: 40%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              // background: rgba(6, 72, 146, 0.35);
              border-radius: vh(4);
              padding: vh(20);

              .reservoir-stats {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

                .stats-grid {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  grid-template-rows: 1fr 1fr;
                  gap: vh(20) vw(15);
                  padding-top: 20px;
                  width: 100%;
                  height: 100%;

                  .stat-item {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    padding: 0 vw(10);

                    .stat-label {
                      font-size: vh(16);
                      color: #ffffff;
                      font-family: JiangChengXieHei;
                      margin-bottom: vh(10);
                      line-height: 1.2;
                      opacity: 0.8;
                    }

                    .stat-value {
                      font-size: vh(28);
                      color: #00d4ff;
                      font-family: JiangChengXieHei;
                      font-weight: bold;

                      .stat-unit {
                        font-size: vh(18);
                        color: #ffffff;
                        margin-left: vw(5);
                        opacity: 0.8;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .third-right {
        width: 100%;
        height: 65%;
        gap: 1%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .third-section-left {
          height: 100%; // 占60%
          width: 49%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);

          .title-area {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: vh(45); // 减小标题区域高度
            padding: 0 0 0 vw(45);
            background-image: url('@/assets/images/home/<USER>');
            background-size: 100% 100%;
            background-repeat: no-repeat;

            .title-left {
              display: flex;
              align-items: center;
              gap: vw(20);

              .main-title {
                font-size: vh(24);
                color: #ffffff;
                font-family: JiangChengXieHei;
                font-weight: normal;
              }
              .title {
                font-weight: 350;
                font-size: vh(20);
                color: #d8f1ff;
                font-style: normal;
                text-transform: none;
              }
            }

            .title-right {
              .warning-count {
                font-size: vh(20); // 减小字体大小
                color: #d8f1ff;
              }
            }
          }

          .tags-container {
            width: 100%;
            height: calc(100% - vh(45));

            .personnel-stats-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              grid-template-rows: 1fr 1fr;
              gap: vh(10) vw(10);
              height: 100%;
              width: 100%;

              .stats-card {
                // background: linear-gradient(135deg, rgba(6, 72, 146, 0.3) 0%, rgba(6, 79, 156, 0.1) 100%);
                // border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: vh(6);
                padding: vh(10) vw(10);
                display: flex;
                flex-direction: column;
                cursor: pointer;
                transition: all 0.3s ease;
                min-height: vh(80);

                .card-header {
                  margin-bottom: vh(6);
                  width: 100%;
                  height: 40px;
                  background-image: url('@/assets/images/home/<USER>');
                  background-size: cover;
                  background-repeat: no-repeat;
                  background-position: center;
                  padding-left: 30px;

                  .card-title {
                    font-size: vh(16);
                    color: #ffffff;
                    font-weight: 500;
                    font-family: JiangChengXieHei;
                  }

                  .card-title {
                    font-size: vh(24);
                    color: #ffffff;
                    font-weight: 500;
                    font-family: JiangChengXieHei;
                  }
                }

                .card-content {
                  flex: 1;
                  display: flex;
                  flex-direction: column;

                  .main-stats {
                    display: flex;
                    gap: vw(12);
                    height: 100%;

                    .percentage-display {
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      // min-width: vw(50);
                      padding-left: 5px;

                      .percentage {
                        font-size: vh(32);
                        color: #ffffff;
                        font-weight: bold;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        line-height: 1;
                        width: 200px;
                        height: 160px;
                        // background-color: red;
                        background-image: url('@/assets/images/home/<USER>');
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                        background-position: center;
                      }

                      .percentage-label {
                        font-size: vh(24);
                        color: #d8f1ff;
                        margin-top: vh(2);
                        text-align: center;
                      }
                    }

                    .stats-details {
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      gap: 20px;

                      .detail-row {
                        display: flex;
                        justify-content: flex-start;
                        flex-direction: column;
                        align-items: flex-start;
                        // gap: 10px;
                        margin-bottom: vh(3);

                        .detail-label {
                          font-size: vh(24);
                          color: #d8f1ff;
                        }

                        .detail-value {
                          font-size: vh(24);
                          color: #ffffff;
                          font-weight: 500;
                        }
                      }
                    }
                  }
                }

                // 不同卡片的特殊样式
                &.personnel-card {
                  .percentage {
                    color: #00ff88;
                  }
                }

                &.supplies-card {
                  .percentage {
                    color: #ffaa00;
                  }
                }

                &.vehicle-card {
                  .percentage {
                    color: #00aaff;
                  }
                }

                &.materials-card {
                  .percentage {
                    color: #ff6600;
                  }
                }
              }
            }
          }
        }

        .third-section-right {
          height: 100%;
          width: 49%;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          border-radius: vh(4);

          .title-area {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: vh(45); // 减小标题区域高度
            padding: 0 0 0 vw(45);
            background-image: url('@/assets/images/home/<USER>');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: left;

            .title-left {
              display: flex;
              align-items: center;
              gap: vw(20);

              .main-title {
                font-size: vh(24);
                color: #ffffff;
                font-family: JiangChengXieHei;
                font-weight: normal;
              }
              .title {
                font-weight: 350;
                font-size: vh(20);
                color: #d8f1ff;
                font-style: normal;
                text-transform: none;
              }
            }

            .title-right {
              .warning-count {
                cursor: pointer;
                font-size: vh(20); // 减小字体大小
                color: #d8f1ff;
              }
            }
          }

          .tags-container {
            width: 100%;
            height: 100%; // 调整高度计算
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            .tags-top {
              height: 50%;
              width: 100%;

              .dispatch-stats-container {
                width: 100%;
                height: 100%;
                display: flex;

                .dispatch-total-section {
                  width: 25%;
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;

                  .dispatch-label {
                    font-size: vh(18);
                    color: #ffffff;
                    margin-bottom: vh(10);
                    font-family: 'Microsoft YaHei';
                  }

                  .dispatch-count {
                    font-size: vh(32);
                    color: #00d4ff;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei';
                  }
                }

                .dispatch-chart-section {
                  width: 75%;
                  height: 100%;

                  .dispatch-chart {
                    width: 100%;
                    height: 100%;
                  }
                }
              }
            }
            .tags-footer {
              height: 50%;
              width: 100%;
              display: flex;
              flex-direction: column;

              .dispatch-table-container {
                flex: 1;
                overflow: hidden;
                margin-top: 10px;
                height: calc(100% - 40px); // 确保容器有明确的高度

                .dispatch-table {
                  width: 100%;
                  height: 100%;
                  border-collapse: collapse;
                  background: transparent;

                  thead {
                    tr {
                      th {
                        background: rgba(0, 152, 250, 0.2);
                        color: #ffffff;
                        font-size: 24px;
                        font-weight: 500;
                        padding: 8px 6px;
                        text-align: center;
                        border: 1px solid rgba(0, 152, 250, 0.3);
                        white-space: nowrap;
                      }
                    }
                  }

                  tbody {
                    display: block;
                    height: calc(100% - 50px);
                    max-height: 220px; // 设置最大高度，确保滚动触发
                    overflow-y: auto;
                    overflow-x: hidden;
                    scrollbar-width: none; /* Firefox */
                    -ms-overflow-style: none; /* IE 和 Edge */
                    &::-webkit-scrollbar {
                      display: none;
                    }

                    tr {
                      display: table;
                      width: 100%;
                      table-layout: fixed;

                      &:nth-child(even) {
                        background: rgba(0, 152, 250, 0.05);
                      }

                      &:hover {
                        background: rgba(0, 152, 250, 0.15);
                        cursor: pointer;
                      }

                      td {
                        color: #ffffff;
                        font-size: 24px;
                        padding: 8px 6px;
                        text-align: center;
                        border: 1px solid rgba(0, 152, 250, 0.2);
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 1.2;
                      }
                    }
                  }

                  thead tr {
                    display: table;
                    width: 100%;
                    table-layout: fixed;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.middle-container {
  left: 46%;
  height: auto;
  .middle-section {
    /* 中间区域标题 */
    .section-title {
      font-family: YouSheBiaoTiHei;
      font-size: vh(80);
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }

    /* 导航按钮 */
    .nav-buttons {
      display: flex;
      justify-content: center;
      gap: vw(20);
      margin-top: vh(60);
    }

    .nav-button {
      width: vw(200);
      height: vh(80);
      background-image: url('@/assets/images/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      padding-top: vh(14);
      font-family: JiangChengXieHei;
      color: #fff;
      font-size: vh(24);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .nav-button.active {
      background-image: url('@/assets/images/home/<USER>');
      color: #fff;
      font-weight: bold;
    }
  }
}

.right-container {
  left: 68%;
  width: 32%;

  .right-section {
    width: 100%;
    height: 100%;
    padding: vh(80) vw(100) vh(20) vw(0);
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .user-portal-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: vh(10) vw(20);
    gap: vw(30);
    border-radius: vh(4);
    background: rgba(6, 72, 146, 0.24); // 添加背景色

    .user-info,
    .portal-back {
      display: flex;
      align-items: center;
      gap: vw(10);

      img {
        width: vh(40);
        height: vh(40);
        border-radius: 50%;
        object-fit: cover;
      }

      span {
        color: #fff;
        font-size: vh(16);
      }
    }
  }

  .right-content {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(19, 44, 57, 0.77) 26%, rgba(19, 44, 57, 0.95) 100%);
    border-radius: vh(4);
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    gap: vw(20);

    .right-content-left {
      // flex: 4;
      width: 33%;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      border-radius: vh(4);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      gap: vh(10);

      .title-area {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: vh(45);
        padding: 0 vw(45);
        background-image: url('@/assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: left;

        .title-left {
          display: flex;
          align-items: center;
          gap: vw(20);

          .title {
            font-size: vh(22);
            color: #ffffff;
            font-family: JiangChengXieHei;
            font-weight: normal;
          }
          .title-number {
            font-size: vh(22);
            color: #d8f1ff;
          }
        }

        .title-right {
          .area {
            font-size: vh(20);
            color: #d8f1ff;
          }
        }
      }
      .video-content {
        padding: vh(10) vw(15);
        height: calc(100% - vh(55));
        overflow: hidden;
      }
      // 视频标签切换
      .video-tabs {
        display: flex;
        width: 100%;
        height: vh(50);
        background: #1b425e;
        border-radius: 2px 2px 2px 2px;
        overflow: hidden;
        // margin-bottom: vh(20);

        .tab-item {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: vh(18);
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          border: 1px solid #2594e4;

          // &:hover {
          //   background: rgba(110, 204, 255, 0.2);
          // }

          &.active {
            background: #2594e4;
            font-weight: 500;
          }
        }
      }

      // 视频网格容器
      .video-grid {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 2列布局 */
        grid-template-rows: repeat(3, 1fr); /* 3行布局，高度平均分配 */
        gap: vh(10); /* 减小间距 */
        padding: vh(20) vw(10) vh(10) vw(10);
        overflow-y: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        height: calc(100% - vh(50)); /* 减去tab导航的高度 */
        justify-items: center; /* 水平居中对齐 */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }
        // 单个视频窗口
        .video-window {
          border-radius: vh(4);
          display: flex;
          flex-direction: column;
          overflow: hidden;
          justify-content: center;
          align-items: center;
          height: 100%; /* 使用100%高度填充网格单元格 */
          width: 100%; /* 使用100%宽度填充网格单元格 */

          // 视频区域 - 现在在上方
          .video-area {
            height: 85%;
            width: 95%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #108dd5;
            padding: vh(2);
            video {
              width: 100%;
              height: 100%;
              object-fit: contain; /* 保持视频比例 */
            }
          }

          // 文字区域 - 现在在下方
          .text-content {
            width: 75%;
            height: 35%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            padding: vh(5);
            gap: vh(5);

            // 标题区域
            .location {
              color: #fff;
              font-size: vh(22);
              font-weight: 700;
              margin-bottom: vh(3);
              text-align: center;
            }

            // 参数显示区域
            .params {
              display: flex;
              flex-direction: row;
              justify-content: space-around;
              gap: vh(10);

              .param-item {
                color: #fff;
                font-size: vh(22);
                display: flex;
                gap: vh(4);
                align-items: center;

                .label {
                  color: #fff;
                }

                .value {
                  color: #fff;
                }
              }
            }
          }
        }
      }

      // 分页控件容器
      .pagination-container {
        padding: vh(15) vw(10);
        // background: rgba(6, 72, 146, 0.2);
        border-radius: 0 0 vh(4) vh(4);
        display: flex;

        :deep(.ant-pagination) {
          .ant-pagination-item,
          .ant-pagination-prev .ant-pagination-item-link,
          .ant-pagination-next .ant-pagination-item-link {
            // background: rgba(255, 255, 255, 0.2);
            // border-color: rgba(255, 255, 255, 0.4);
            color: #ffffff;

            &:hover {
              border-color: #6eccff;
              color: #6eccff;
            }
          }

          .ant-pagination-item-active {
            background: rgba(6, 72, 146, 0.8);
            border-color: #6eccff;

            a {
              color: #ffffff;
            }
          }

          .ant-pagination-options {
            .ant-select-selector {
              // background: rgba(6, 72, 146, 0.6);
              // border-color: rgba(255, 255, 255, 0.4);
              color: #ffffff;
            }

            .ant-select-arrow {
              color: #ffffff;
            }
          }

          .ant-pagination-total-text {
            color: #ffffff;
          }
        }
      }
    }

    .right-content-middle {
      // flex: 2;
      width: 33%;
      border-radius: vh(4);
      display: flex;
      flex-direction: column;
      gap: vh(10);

      .middle-top-section {
        width: 100%;
        height: 50%;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(10);

        .title-area {
          display: flex;
          justify-content: space-between;
          gap: 20px;
          align-items: center;
          height: vh(45);
          padding: 0 0 0 vw(45);
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: left;

          .title-left {
            display: flex;
            align-items: center;
            gap: vw(20);

            .title {
              font-size: vh(22);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }
            .title-desc {
              font-size: vh(22);
              color: #ffffff;
            }
          }

          .title-right {
            .area {
              font-size: vh(20);
              color: #d8f1ff;
            }
          }
        }

        /* middle-top-section-table 样式 */
        .middle-top-section-table {
          flex: 1;
          padding: vh(8) vw(12);
          display: flex;
          flex-direction: column;
          gap: vh(12);

          /* 统计卡片样式 */
          .stats-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: vw(8);
            margin-bottom: vh(10);
            padding: 0 vw(3);

            .stats-card {
              // background: rgba(6, 72, 146, 0.4);
              // border: 1px solid rgba(24, 144, 255, 0.6);
              border-radius: vh(6);
              padding: vh(8) vw(12);
              text-align: center;
              min-width: vw(80);
              display: flex;
              justify-content: space-around;
              align-items: center;
              cursor: pointer;
              .stats-img {
                // width: 160px;
                // height: 140px;
                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .stats-title {
                font-size: vh(24);
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: vh(4);
                white-space: nowrap;
              }

              .stats-value {
                font-size: vh(24);
                color: #fff;
                font-weight: bold;
                white-space: nowrap;
              }
            }
          }

          /* 更多链接区域样式 */
          .more-section {
            display: flex;
            justify-content: flex-end;

            .more-link {
              color: #52c41a;
              font-size: vh(20);
              cursor: pointer;
              white-space: nowrap;
              border-radius: vh(3);
              transition: all 0.3s ease;

              &:hover {
                color: #73d13d;
                // background: rgba(82, 196, 26, 0.1);
              }
            }
          }

          /* 表格包装器 */
          .warning-table-wrapper {
            background: rgba(6, 72, 146, 0.15);
            border-radius: vh(4);
            overflow: hidden;
            flex: 1;
            border: 1px solid rgba(24, 144, 255, 0.2);
          }

          .warning-table {
            width: 100%;
            border-collapse: collapse;
            color: #fff;
            display: flex;
            flex-direction: column;
            height: 100%;

            thead {
              background: linear-gradient(135deg, rgba(6, 72, 146, 0.5) 0%, rgba(24, 144, 255, 0.3) 100%);
              display: block;
              width: 100%;

              tr {
                display: flex;
                width: 100%;
              }

              th {
                padding: vh(8) vw(10);
                text-align: center;
                font-size: vh(24);
                font-weight: 600;
                color: rgba(255, 255, 255, 0.95);
                border-right: 1px solid rgba(255, 255, 255, 0.15);
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.5;
                position: relative;

                &:last-child {
                  border-right: none;
                }

                &::after {
                  content: '';
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  right: 0;
                  height: 1px;
                  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
                }
              }
            }

            tbody {
              height: vh(340);
              overflow-y: auto;
              scrollbar-width: none; /* Firefox */
              -ms-overflow-style: none; /* IE 和 Edge */
              display: block;
              width: 100%;
              background: rgba(9, 49, 84, 0.6);

              &::-webkit-scrollbar {
                display: none; /* Chrome, Safari, Opera */
              }

              tr {
                display: flex;
                width: 100%;
                cursor: pointer;
                transition: all 0.2s ease;

                td {
                  padding: vh(8) vw(10);
                  font-size: vh(24);
                  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
                  border-right: 1px solid rgba(255, 255, 255, 0.06);
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  text-align: center;
                  line-height: 1.5;
                  color: rgba(255, 255, 255, 0.9);

                  &:last-child {
                    border-right: none;
                  }

                  /* 报警等级样式 */
                  &.level-1 {
                    color: #ff4d4f;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(255, 77, 79, 0.3);
                  }

                  &.level-2 {
                    color: #ff7a45;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(255, 122, 69, 0.3);
                  }

                  &.level-3 {
                    color: #ffa940;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(255, 169, 64, 0.3);
                  }

                  &.level-4 {
                    color: #52c41a;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(82, 196, 26, 0.3);
                  }
                }
              }
            }
          }
        }
      }

      .middle-bottom-section {
        width: 100%;
        height: 50%;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(10);

        .title-area {
          display: flex;
          justify-content: space-between;
          gap: 20px;
          align-items: center;
          height: vh(45);
          padding: 0 0 0 vw(45);
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;

          .title-left {
            display: flex;
            align-items: center;
            gap: vw(20);

            .title {
              font-size: vh(22);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }
            .title-desc {
              font-size: vh(22);
              color: #ffffff;
            }
          }

          .title-right {
            .area {
              font-size: vh(20);
              cursor: pointer;
              color: #d8f1ff;
            }
          }
        }

        .middle-bottom-section-table {
          flex: 1;
          padding: vh(8) vw(12);
          display: flex;
          flex-direction: column;
          gap: vh(12);

          /* 统计卡片容器样式 */
          .stats-cards-container {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: vh(10);
            gap: vw(12);

            .stats-cards-grid {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              grid-template-rows: repeat(2, 1fr);
              gap: vw(8) vh(8);
              flex: 1;

              .stats-card {
                // background: rgba(6, 72, 146, 0.4);
                // border: 1px solid rgba(24, 144, 255, 0.6);
                border-radius: vh(6);
                padding: vh(8) vw(12);
                text-align: center;
                min-width: vw(80);
                display: flex;
                justify-content: space-around;
                align-items: center;
                cursor: pointer;
                .stats-img {
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .stats-title {
                  font-size: vh(24);
                  color: rgba(255, 255, 255, 0.9);
                  margin-bottom: vh(4);
                  white-space: nowrap;
                }

                .stats-value {
                  font-size: vh(24);
                  color: #fff;
                  font-weight: bold;
                  white-space: nowrap;
                }
              }
            }
          }
          .more-link {
            color: #52c41a;
            width: 100%;
            font-size: vh(20);
            cursor: pointer;
            align-self: flex-start;
            display: flex;
            justify-content: flex-end;
            &:hover {
              color: #73d13d;
            }
          }
          /* 表格样式 */
          .warning-table-wrapper {
            flex: 1;
            overflow: hidden;

            .warning-table {
              width: 100%;
              border-collapse: collapse;
              color: #fff;
              font-size: vh(24);
              th {
                background: rgba(52, 120, 187, 0.3632);
                padding: vh(8) vw(12);
                text-align: center;
                font-weight: bold;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                font-size: vh(24);
              }

              td {
                padding: vh(8) vw(12);
                text-align: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                font-size: vh(24);
              }

              tr:hover {
                background-color: rgba(0, 162, 236, 0.1);
              }

              /* 报警等级样式 */
              .level-1 {
                color: #ff4d4f;
                font-weight: bold;
              }

              .level-2 {
                color: #ff7a45;
                font-weight: bold;
              }

              .level-3 {
                color: #ffa940;
                font-weight: bold;
              }

              .level-4 {
                color: #52c41a;
                font-weight: bold;
              }
            }
          }
          .warning-table {
            width: 100%;
            border-collapse: collapse;
            color: #fff;
            display: flex;
            flex-direction: column;
            height: 100%;

            thead {
              background: linear-gradient(135deg, rgba(6, 72, 146, 0.5) 0%, rgba(24, 144, 255, 0.3) 100%);
              display: block;
              width: 100%;

              tr {
                display: flex;
                width: 100%;
              }

              th {
                padding: vh(8) vw(10);
                text-align: center;
                font-size: vh(24);
                font-weight: 600;
                color: rgba(255, 255, 255, 0.95);
                border-right: 1px solid rgba(255, 255, 255, 0.15);
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.5;
                position: relative;

                &:last-child {
                  border-right: none;
                }

                &::after {
                  content: '';
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  right: 0;
                  height: 1px;
                  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
                }
              }
            }

            tbody {
              height: vh(220);
              overflow-y: auto;
              scrollbar-width: none; /* Firefox */
              -ms-overflow-style: none; /* IE 和 Edge */
              display: block;
              width: 100%;
              background: rgba(9, 49, 84, 0.6);

              &::-webkit-scrollbar {
                display: none; /* Chrome, Safari, Opera */
              }

              tr {
                display: flex;
                width: 100%;
                cursor: pointer;
                transition: all 0.2s ease;

                td {
                  padding: vh(8) vw(10);
                  font-size: vh(24);
                  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
                  border-right: 1px solid rgba(255, 255, 255, 0.06);
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  text-align: center;
                  line-height: 1.5;
                  color: rgba(255, 255, 255, 0.9);

                  &:last-child {
                    border-right: none;
                  }

                  /* 报警等级样式 */
                  &.level-1 {
                    color: #ff4d4f;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(255, 77, 79, 0.3);
                  }

                  &.level-2 {
                    color: #ff7a45;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(255, 122, 69, 0.3);
                  }

                  &.level-3 {
                    color: #ffa940;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(255, 169, 64, 0.3);
                  }

                  &.level-4 {
                    color: #52c41a;
                    font-weight: bold;
                    text-shadow: 0 0 4px rgba(82, 196, 26, 0.3);
                  }
                }
              }
            }
          }
        }
      }
    }

    .right-content-right {
      // flex: 4;
      width: 33%;
      background: rgba(6, 72, 146, 0.24);
      border-radius: vh(4);
      display: flex;
      flex-direction: column;
      gap: vh(20);

      .middle-center-section {
        flex: 3;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(10);

        .title-area {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: vh(45);
          padding: 0 0 0 vw(45);
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;

          .title-left {
            display: flex;
            align-items: center;
            gap: vw(20);

            .title {
              font-size: vh(22);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }
          }

          .title-right {
            .area {
              font-size: vh(20);
              color: #d8f1ff;
            }
          }
        }

        .chart-area {
          flex: 1;
          width: 100%;
          min-height: vh(300);

          .vertical-bar-chart2 {
            width: 100%;
            height: 100%;
            min-height: vh(300);
          }
        }
      }

      .middle-bottom-section {
        flex: 3;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(10);

        .title-area {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: vh(45);
          padding: 0 0 0 vw(45);
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;

          .title-left {
            display: flex;
            align-items: center;
            gap: vw(20);

            .title {
              font-size: vh(22);
              color: #ffffff;
              font-family: JiangChengXieHei;
              font-weight: normal;
            }
          }

          .title-right {
            .area {
              font-size: vh(20);
              cursor: pointer;
              color: #d8f1ff;
            }
          }
        }

        .table-container {
          flex: 1;
          padding: vh(3) vw(10) vh(10) vw(10);

          /* 统计卡片样式 */
          .stats-cards {
            display: flex;
            align-items: center;
            margin-bottom: vh(20);
            gap: vw(20);

            .stats-card {
              background: rgba(6, 72, 146, 0.4);
              border: 1px solid rgba(24, 144, 255, 0.6);
              border-radius: vh(6);
              padding: vh(12) vw(16);
              text-align: center;
              min-width: vw(100);
              flex: 1;

              .stats-title {
                font-size: vh(16);
                color: rgba(255, 255, 255, 0.9);
                margin-bottom: vh(6);
                white-space: nowrap;
              }

              .stats-value {
                font-size: vh(20);
                color: #fff;
                font-weight: bold;
                white-space: nowrap;
              }
            }

            .more-link {
              color: #52c41a;
              font-size: vh(16);
              cursor: pointer;
              margin-left: auto;
              padding: vh(8) vw(12);

              &:hover {
                color: #73d13d;
              }
            }
          }

          /* 表格包装器 */
          .warning-table-wrapper {
            background: rgba(6, 72, 146, 0.2);
            border-radius: vh(8);
            overflow: hidden;
          }

          .warning-table {
            width: 100%;
            border-collapse: collapse;
            color: #fff;
            display: flex;
            flex-direction: column;
            height: 100%;
            table-layout: fixed; /* 添加固定表格布局 */

            thead {
              background: rgba(6, 72, 146, 0.4);
              display: block; /* 添加显示方式 */
              width: 100%; /* 设置宽度 */

              tr {
                display: flex; /* 使用flex布局 */
                width: 100%;
              }

              th {
                padding: vh(12) vw(15);
                text-align: left;
                font-size: vh(22);
                font-weight: normal;
                color: rgba(255, 255, 255, 0.9);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                flex: 1; /* 平均分配宽度 */
                white-space: nowrap; /* 防止文字换行 */
                overflow: hidden;
                text-overflow: ellipsis; /* 文字溢出显示省略号 */
              }
            }

            tbody {
              height: vh(240);
              overflow-y: auto;
              scrollbar-width: none; /* Firefox */
              -ms-overflow-style: none; /* IE 和 Edge */
              display: block; /* 添加显示方式 */
              width: 100%; /* 确保宽度与表头一致 */
              background: #093154;
              &::-webkit-scrollbar {
                display: none; /* Chrome, Safari, Opera */
              }

              tr {
                display: flex; /* 使用flex布局 */
                width: 100%;

                td {
                  padding: vh(12) vw(15);
                  font-size: vh(24);
                  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                  flex: 1; /* 平均分配宽度 */
                  white-space: nowrap; /* 防止文字换行 */
                  overflow: hidden;
                  text-overflow: ellipsis; /* 文字溢出显示省略号 */

                  /* 报警等级样式 */
                  &.level-1 {
                    color: #ff4d4f;
                    font-weight: bold;
                  }

                  &.level-2 {
                    color: #ff7a45;
                    font-weight: bold;
                  }

                  &.level-3 {
                    color: #ffa940;
                    font-weight: bold;
                  }

                  &.level-4 {
                    color: #52c41a;
                    font-weight: bold;
                  }
                }
              }
            }
          }
        }
      }

      .right-bottom-section {
        flex: 4;
        background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
        border-radius: vh(4);
        display: flex;
        flex-direction: column;
        gap: vh(15);

        // 标题区域
        .section-header {
          background-image: url('@/assets/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;
          height: vh(45);
          padding: 0 0 0 vw(45);
          display: flex;
          justify-content: space-between;
          align-items: center;

          .header-title {
            font-size: vh(22);
            color: #ffffff;
            font-family: JiangChengXieHei;
            font-weight: normal;
          }
          .more {
            color: #d8f1ff;
            font-size: vh(20);
            cursor: pointer;
          }
          .header-extra {
            font-size: vh(20);
            color: #d8f1ff;
          }
        }

        // 告警列表容器
        .alert-list-container {
          position: relative;
          height: vh(380);
          overflow: hidden;
          padding: vh(10) 0;

          .alert-items-wrapper {
            animation: scrollAnimation 20s linear infinite;
            padding: vh(0) vw(10);
            height: 100%;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            &:hover {
              animation-play-state: paused;
            }
          }

          .alert-item {
            padding: vh(15) vw(10);
            color: #ffffff;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            min-height: vh(80);

            .alert-content-container {
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: flex-start;
              margin-right: vw(15);

              .alert-content {
                font-size: vh(22);
                line-height: 1.6;
                margin-bottom: vh(10);
                text-align: left;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
              }

              .alert-time {
                font-size: vh(18);
                color: #949fbc;
                align-self: left;
              }
            }

            .alert-action-container {
              display: flex;
              align-items: center;
              justify-content: flex-end;

              .verify-button {
                width: vh(70);
                height: vh(30);
                border-radius: vh(4);
                font-size: vh(18);
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                  opacity: 0.9;
                }

                &[status='已核实'] {
                  background: rgba(0, 255, 30, 0.1);
                  border: 1px solid #00ff1e;
                  color: #00ff1e;
                }

                &[status='未核实'] {
                  background: rgba(255, 98, 0, 0.1);
                  border: 1px solid #ff6200;
                  color: #ff6200;
                }
              }
            }
          }
        }

        @keyframes scrollAnimation {
          0% {
            transform: translateY(0);
          }
          100% {
            transform: translateY(-50%);
          }
        }
      }
    }
  }
}

.warning-table tbody tr {
  cursor: pointer;
}

.warning-table tr {
  cursor: pointer;
}
.rainfall-table tbody tr {
  cursor: pointer;
}
.rainfall-table tr {
  cursor: pointer;
}
:deep(.el-dialog) {
  --el-dialog-bg-color: transparent;
  background-image: url('@/assets/images/tcbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* 弹窗样式 */
.custom-dialog {
  --el-dialog-text-color: #fff;
  --el-dialog-border-radius: 8px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-top: 40px;

  // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  color: #fff;
  margin-right: 10px;
  white-space: nowrap;
}

.table-section {
  flex: 1;
  overflow: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  color: #fff;
}

.data-table th,
.data-table td {
  padding: 12px 8px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table th {
  background-color: rgba(0, 162, 236, 0.2);
  font-weight: bold;
}

.data-table tr:hover {
  background-color: rgba(0, 162, 236, 0.1);
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 覆盖Element Plus默认样式 */
:deep(.el-button) {
  --el-button-bg-color: rgba(0, 162, 236, 0.8);
  --el-button-border-color: rgba(0, 162, 236, 0.8);
  --el-button-hover-bg-color: rgba(0, 162, 236, 1);
  --el-button-hover-border-color: rgba(0, 162, 236, 1);
}

/* 仓库下拉按钮样式 */
.warehouse-dropdown-button {
  width: 300px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  // padding: 0 12px !important;
  position: relative !important;
  background: transparent !important;

  .button-text {
    flex: 1;
    text-align: left;
    // overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
  }

  .dropdown-icon {
    flex-shrink: 0;
    margin-left: auto;
    position: absolute;
    right: 12px;
  }
}

// :deep(.el-input__wrapper) {
// background-color: rgba(255, 255, 255, 0.1);
// border: 1px solid rgba(255, 255, 255, 0.2);
// }

:deep(.el-input__inner) {
  color: #fff;
}

:deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #ffffff;
  --el-pagination-button-color: #ffffff;
  --el-pagination-hover-color: #00a2ec;
  --el-pagination-button-bg-color: rgba(255, 255, 255, 0.2);
  --el-pagination-button-disabled-bg-color: rgba(255, 255, 255, 0.1);

  .el-pagination__total,
  .el-pagination__sizes,
  .el-pagination__jump {
    color: #ffffff;
    font-weight: bold;
  }

  .btn-prev,
  .btn-next,
  .el-pager li {
    background-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    // border: 1px solid rgba(255, 255, 255, 0.4);

    &:hover {
      color: #00a2ec;
      border-color: #00a2ec;
    }

    &.is-active {
      background-color: rgba(0, 162, 236, 0.6);
      color: #ffffff;
      font-weight: bold;
    }
  }
}

:deep(.el-dialog__title) {
  color: #fff;
}

:deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: #00a2ec;
}

/* 弹窗样式 */
.supplies-dialog {
  pointer-events: auto;
  .el-overlay-dialog {
    pointer-events: auto;
    .el-dialog {
      pointer-events: auto;
    }
  }
}

.supplies-dialog {
  .el-dialog {
    background-image: url('@/assets/images/tcbg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 0;
    .el-dialog__header {
      height: 50px;
      // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
      border-radius: 0px 0px 0px 0px;
      padding-left: 30px;
      .el-dialog__title {
        line-height: 50px;
        color: #fff;
        font-size: 24px;
      }
      .el-dialog__close {
        color: #fff;
        font-size: 30px;
      }
    }
    .el-dialog__body {
      padding-left: 50px;
      padding-right: 50px;
      padding-bottom: 80px;
      .el-form-item__label {
        color: #fff;
        font-size: 22px;
      }
      .el-select__wrapper {
        background: transparent;
        // box-shadow: 0 0 0 1px #4190d8 inset;
        .el-select__selected-item {
          color: #fff;
          font-size: 18px;
        }
      }
      .el-input__wrapper {
        background: transparent;
        // box-shadow: 0 0 0 1px #4190d8 inset;
        .el-input__inner {
          color: #fff;
          font-size: 18px;
        }
        .el-input__inner::-webkit-input-placeholder {
          color: #fff;
        }
        .el-input__inner::-moz-placeholder {
          color: #fff;
        }
      }
      .el-button {
        background: rgba(0, 147, 255, 0.2);
        border: 1px solid #1f8ad4;
        font-family:
          Source Han Sans,
          Source Han Sans;
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .el-table__row {
        background: transparent;
        td {
          background: transparent;
          color: #fff;
          font-size: 22px;
          border-bottom: 1px solid rgba(216, 216, 216, 0.2);
          padding: 16px 0;
        }
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background-color: rgba(31, 138, 212, 0.4);
      }
      .el-table__header-wrapper {
        tr {
          background: transparent;
          background: rgba(52, 120, 187, 0.3632);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid rgba(60, 139, 217, 0.4542);
          th {
            background: transparent;
            font-size: 20px;
            color: #fff;
            border-bottom: none;
          }
        }
      }

      .el-table--fit .el-table__inner-wrapper::before {
        width: 0px;
      }

      .el-pager {
        li {
          background: transparent;
          color: #d8d8d8;
          font-size: 18px;
          &.is-active {
            background: #008aff;
          }
        }
      }
      .el-pagination {
        float: right;
        button {
          background: transparent;
        }
        .btn-prev,
        .btn-next {
          color: #fff;
        }
        .el-pagination__total,
        .el-pagination__jump {
          color: #fff;
          font-size: 18px;
        }
      }
    }
  }
}

.supplies-table {
  color: #fff;
  width: 100%;
  table-layout: fixed;

  thead {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  th {
    // background: rgba(52, 120, 187, 0.3632);
    text-align: left;
    font-weight: bold;
    font-size: 20px;
    color: #fff;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  tbody {
    display: block;
    max-height: 600px;
    overflow-y: auto;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 152, 250, 0.5);
      border-radius: 3px;
    }
  }

  tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  td {
    font-size: 20px;
    color: #fff;
    text-align: left;
  }
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;

  :deep(.el-pagination) {
    --el-pagination-bg-color: transparent;
    --el-pagination-text-color: #ffffff;
    --el-pagination-button-color: #ffffff;
    --el-pagination-hover-color: #00a2ec;
    // --el-pagination-button-bg-color: rgba(255, 255, 255, 0.2);
    // --el-pagination-button-disabled-bg-color: rgba(255, 255, 255, 0.1);

    .el-pagination__total,
    .el-pagination__sizes,
    .el-pagination__jump {
      color: #ffffff;
      font-weight: bold;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      // background-color: rgba(255, 255, 255, 0.2);
      color: #ffffff;
      // border: 1px solid rgba(255, 255, 255, 0.4);

      &:hover {
        color: #00a2ec;
        border-color: #00a2ec;
      }

      &.is-active {
        background-color: rgba(0, 162, 236, 0.6);
        color: #ffffff;
        font-weight: bold;
      }
    }
  }
}

.select-popper {
  .el-select-dropdown__item {
    font-size: 18px;
  }
}
</style>

<style lang="scss" scoped>
// :deep(.supplies-dialog .el-dialog .el-dialog__header) {
//   background: transparent !important;
// }
// :deep(.supplies-dialog .el-dialog) {
//   background: transparent !important;
// }
// :deep(.supplies-dialog .el-dialog) {
//   background-image: url('@/assets/images/tcbg.png');
//   background-size: 100% 100%;
//   background-repeat: no-repeat;
//   // background: none !important;
//   box-shadow: none !important;
// }
// :deep(.supplies-dialog .el-dialog .el-dialog__body .el-table__header-wrapper tr) {
//   background: #1b72df;
// }

/* 舆情信息弹窗table居中样式 */
:deep(.sentiment-table-center) {
  .el-table__header th {
    text-align: center !important;
    font-size: 24px;
  }

  .el-table__body td {
    text-align: center !important;
    font-size: 24px;
  }
}
</style>
