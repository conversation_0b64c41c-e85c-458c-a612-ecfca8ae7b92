<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>模拟卷帘效果</title>
    <script src="ac.min.js"></script>
    <script src="jquery-3.7.0.min.js"></script>
    <style>
        #player1 {
            position: absolute;
            top: 0px;
            left: 0px;

        }

        #player2 {
            position: absolute;
            top: 0px;
            left: 0px;
        }

        .video-container {
            position: relative;
            width: 960px;
            /* 根据需要设置宽度 */
            height: 540px;
            /* 根据需要设置高度 */
            overflow: hidden;
        }

        .streamingVideoCursorPointer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            /* 根据需要设置宽度 */
            height: 100%;
            /* 根据需要设置高度 */
        }

        #streamingVideo_1 {
            /* 显示左部分 */
            position: absolute;
            top: 0px;
            left: 0px;
            z-index: 999;
        }

        #streamingVideo_2 {
            position: absolute;
            top: 0px;
            left: 0px;
            clip-path: inset(0 0 0 50%);
            /* 显示右部分 */
            z-index: 999;
        }
    </style>
</head>

<body>
    <div class="video-container" id="video-container">
        <div id="player1" style="width: 960px;height: 540px;"></div>
        <div id="player2" style="width: 960px;height: 540px;"></div>
    </div>

    <br><b>示例功能按钮：</b>
    <button id="bindJL" onclick="bindJL();">进入卷帘</button>
    <button id="unbindJL" onclick="unbindJL();">退出卷帘</button>

    <script>

        function RollImage(evt) {
            var x = evt.pageX;
            //console.log(x);
            //$("#player2").css("width",x+"px");
            $("#streamingVideo_2").css("clip-path", "inset(0 0 0 " + (100 * x / 960) + "%)");
        }

        function bindJL() {
            //绘制竖线
            var line = document.createElement('div');

            //激活video窗口
            $("#streamingVideo_2").focus();
            $("#streamingVideo_2").trigger("click");
            $("#streamingVideo_2").trigger("click");


            $('#video-container').mousemove(function (event) {
                var x = event.pageX;
                //console.log(x);
                //$("#player2").css("width",x+"px");
                $("#streamingVideo_2").css("clip-path", "inset(0 0 0 " + (100 * x / 960) + "%)");
                line.id = "line";
                line.style.position = 'absolute';
                line.style.top = '0';
                line.style.left = x + 'px';
                line.style.width = '2px';
                line.style.height = '100%';
                line.style.backgroundColor = 'yellow';
                line.style.zIndex = 999;
                document.getElementById('video-container').appendChild(line);

            });

            api1.settings.setEnableInteract(false);
            api2.settings.setEnableInteract(false);
            api1.camera.set(490256.214375, 2495216.6375, 188.812012, -31.755644, 52.429024, 0);
            api2.camera.set(490256.214375, 2495216.6375, 88.812012, -31.755644, 52.429024, 0);

        }

        function unbindJL() {
            $("#line").remove();
            $("#video-container").off("mousemove");
            $("#streamingVideo_2").css("clip-path", "inset(0 0 0 0%)");
            api1.settings.setEnableInteract(true);
            api2.settings.setEnableInteract(true);
        }


        //api初始化完成全局标识
        var apiOnReady = false;
        //AirCloud云渲染服务器地址和端口
        // var host = '**************:8081';
        var host = '***************:8080'

        //非常重要 这里是所有API接口调用的入口
        function _onReady1() {
            apiOnReady = true;
            //隐藏界面UI
            api1.settings.setMainUIVisibility(false);
            //隐藏指北针
            api1.settings.setCampassVisible(false);

        }

        function _onReady2() {
            apiOnReady = true;
            //隐藏界面UI
            api2.settings.setMainUIVisibility(false);
            //隐藏指北针
            api2.settings.setCampassVisible(false);

        }


        //事件交互的专题 
        function _onEvent(event) {
            if (event.eventtype == 'LeftMouseButtonClick' && event.Type == 'TileLayer') {
                console.info("当前点击图层位置：" + event.MouseClickPoint);
                //高亮actor
                //fdapi.tileLayer.stopHighlightActor(event.Id, event.ObjectID);

            }

            if (event.eventtype == 'LeftMouseButtonClick' && event.Type == 'marker') {

                //fdapi.marker.delete(event.Id);

            }
        }

        //调试输出日志
        function _onLog(s, nnl) {
            var logStr = (s + (nnl ? '' : '\n'));
            console.info(logStr);
        }

        //构造AirCityPlayer对象所需的参数选项，更多参数详情请参考API开发手册里AirCityPlayer对象
        var options1 = {
            //必选参数，网页显示视频流的domId
            'domId': 'player1',

            //'pid': '',

            // 'iid': '2551604637832',
            'id': '2551604637832',
            'ui': {
                startupInfo: false,          //默认值为true，初始化过程中是否显示详细信息（如果不需要，直接去掉此属性即可）
                statusIndicator: false,      //默认值为true，左上角闪烁的状态指示灯，可以从不同的颜色看出当前的状态
                statusButton: false,         //默认值为false，是否在左下角显示信息按钮（如果不需要，直接去掉此属性即可）
                fullscreenButton: false,     //默认值为false，是否在右下角显示全屏按钮（如果不需要，直接去掉此属性即可）
                homeButton: false,           //默认值为false，是否在左下角显示“回到初始位置”按钮（如果不需要，直接去掉此属性即可）
                //taskListBar: 0,             //默认值为1，是否在下方显示“任务队列（API调用队列）”信息（0: 永不显示；1: 执行比较耗时的操作时显示；2: 一直显示）
                //debugEventsPanel: urlParams.get('debugEventsPanel'),   //是否显示事件详细信息（仅用于调试）
                mainUI: false,            //是否显示Cloud工具栏，如果设置为true就显示，如果设置为false就隐藏，如果没有设置，就保持原状。
                campass: false,           //是否显示指北针，如果设置为true就显示，如果设置为false就隐藏，如果没有设置，就保持原状。
            },

            //必选参数，二次开发时必须指定，否则无法进行二次开发
            'apiOptions': {

                //必选参数，与云渲染主机通信成功后的回调函数
                //注意：对场景的一切操作, 请在onready之后执行
                'onReady': _onReady1,

                //可选参数，日志输出回调函数 开发调试需要
                //'onLog': _onLog,

                //可选参数，三维场景交互事件回调函数

                //'onEvent': _onEvent,

                //可选参数，日志颜色，默认关闭
                //'useColorLog': false
            },

            //     //可选参数，是否显示页面按钮【+显示信息】，默认false
            //     'showMarker': true,
            //     //可选参数，是否显示页面加载详细信息，默认值为true
            //     'showStartupInfo': true,
            //     //可选参数，视频流加载成功回调函数
            //     'onloaded': _onLoaded,
            //     //可选参数，连接断开回调函数
            //     'onclose': _onClose,
            //     //可选参数，设置三维交互的键盘事件接收者
            //     //注意：接收类型有视频标签(video)，网页文档(document)，空(none)
            //     'keyEventReceiver': 'none',
        };
        //构造api
        var api1 = new DigitalTwinPlayer(host, options1).getAPI();

        var options2 = {
            //必选参数，网页显示视频流的domId
            'domId': 'player2',

            //'pid': '',

            // 'iid': '2551663703769',
            'id': '2551663703769',

            'ui': {
                startupInfo: false,          //默认值为true，初始化过程中是否显示详细信息（如果不需要，直接去掉此属性即可）
                statusIndicator: false,      //默认值为true，左上角闪烁的状态指示灯，可以从不同的颜色看出当前的状态
                statusButton: false,         //默认值为false，是否在左下角显示信息按钮（如果不需要，直接去掉此属性即可）
                fullscreenButton: false,     //默认值为false，是否在右下角显示全屏按钮（如果不需要，直接去掉此属性即可）
                homeButton: false,           //默认值为false，是否在左下角显示“回到初始位置”按钮（如果不需要，直接去掉此属性即可）
                //taskListBar: 0,             //默认值为1，是否在下方显示“任务队列（API调用队列）”信息（0: 永不显示；1: 执行比较耗时的操作时显示；2: 一直显示）
                //debugEventsPanel: urlParams.get('debugEventsPanel'),   //是否显示事件详细信息（仅用于调试）
                mainUI: false,            //是否显示Cloud工具栏，如果设置为true就显示，如果设置为false就隐藏，如果没有设置，就保持原状。
                campass: false,           //是否显示指北针，如果设置为true就显示，如果设置为false就隐藏，如果没有设置，就保持原状。
            },

            //必选参数，二次开发时必须指定，否则无法进行二次开发
            'apiOptions': {

                //必选参数，与云渲染主机通信成功后的回调函数
                //注意：对场景的一切操作, 请在onready之后执行
                'onReady': _onReady2,

                //可选参数，日志输出回调函数 开发调试需要
                //'onLog': _onLog,

                //可选参数，三维场景交互事件回调函数

                //'onEvent': _onEvent,

                //可选参数，日志颜色，默认关闭
                //'useColorLog': false
            },

            //     //可选参数，是否显示页面按钮【+显示信息】，默认false
            //     'showMarker': true,
            //     //可选参数，是否显示页面加载详细信息，默认值为true
            //     'showStartupInfo': true,
            //     //可选参数，视频流加载成功回调函数
            //     'onloaded': _onLoaded,
            //     //可选参数，连接断开回调函数
            //     'onclose': _onClose,
            //     //可选参数，设置三维交互的键盘事件接收者
            //     //注意：接收类型有视频标签(video)，网页文档(document)，空(none)
            //     'keyEventReceiver': 'none',
        };
        //构造api
        var api2 = new DigitalTwinPlayer(host, options2).getAPI();


        function hideUI() {

        }






    </script>

</body>

</html>