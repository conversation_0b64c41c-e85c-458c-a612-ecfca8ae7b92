# 降雨日历Cell样式调试记录

## 问题描述
降雨日历的cell样式没有生效，需要参照辅助决策页面的样式进行复刻。

## 尝试的解决方案

### 1. 第一次尝试：Scoped样式 + :deep()
```scss
:deep(.cell) {
  padding: 5px 0;
  // ... 其他样式
}
```
**结果**: 样式可能没有生效，因为Element Plus组件的样式优先级较高。

### 2. 第二次尝试：在date-picker-container内部使用:deep()
```scss
.date-picker-container {
  :deep(.cell) {
    padding: 5px 0;
    // ... 其他样式
  }
}
```
**结果**: 更具体的选择器，但仍可能被Element Plus样式覆盖。

### 3. 第三次尝试：添加!important提高优先级
```scss
:deep(.cell) {
  padding: 5px 0 !important;
  display: flex !important;
  // ... 所有样式都加上!important
}
```
**结果**: 强制提高样式优先级。

### 4. 第四次尝试：全局样式
```scss
/* 在非scoped的style标签中 */
.cell {
  padding: 5px 0 !important;
  // ... 其他样式
}
```
**结果**: 全局样式，不受scoped限制，优先级最高。

## 当前实现

现在文件中同时包含了两套样式：

### A. Scoped样式（在date-picker-container内部）
```scss
.date-picker-container {
  :deep(.cell) {
    padding: 5px 0 !important;
    box-sizing: border-box !important;
    // ... 完整样式
  }
}
```

### B. 全局样式
```scss
.cell {
  padding: 5px 0 !important;
  box-sizing: border-box !important;
  // ... 完整样式
}
```

## 样式内容

### 基础cell样式
- padding: 5px 0
- display: flex, flex-direction: column
- align-items: center, justify-content: center

### has-rain样式（有降雨的日期）
- background-color: rgba(64, 158, 255, 0.1)
- border-radius: 4px

### disabled样式（禁用的日期）
- color: #c0c4cc
- background-color: #f5f7fa
- cursor: not-allowed
- 隐藏precipitation显示

### precipitation样式（降雨量文字）
- font-size: 12px
- color: #409eff
- font-weight: bold
- margin-top: 2px

## 验证方法

1. 打开浏览器开发者工具
2. 点击降雨日历标题打开日期选择器
3. 检查cell元素的样式是否正确应用
4. 查看有降雨的日期是否有蓝色背景
5. 查看降雨量文字是否正确显示

## 最终解决方案：内联样式

### ✅ 问题解决
经过多次尝试，最终使用**内联样式**的方法成功解决了样式不生效的问题。

### 🔧 解决方案详情

#### 1. 使用内联样式替代CSS类
```vue
<div
  class="cell"
  :style="{
    padding: '5px 0',
    boxSizing: 'border-box',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: hasValidPrecipitation(cell) ? 'rgba(64, 158, 255, 0.1)' : (isDateDisabled(cell) ? '#f5f7fa' : 'transparent'),
    borderRadius: hasValidPrecipitation(cell) || isDateDisabled(cell) ? '4px' : '0',
    color: isDateDisabled(cell) ? '#c0c4cc' : 'inherit',
    cursor: isDateDisabled(cell) ? 'not-allowed' : 'pointer'
  }"
>
```

#### 2. 动态样式绑定
- **有降雨的日期**: `backgroundColor: 'rgba(64, 158, 255, 0.1)'`
- **禁用的日期**: `backgroundColor: '#f5f7fa'`, `color: '#c0c4cc'`
- **正常日期**: `backgroundColor: 'transparent'`

#### 3. 修复isDateDisabled函数
```javascript
const isDateDisabled = cell => {
  if (!holidays.value || holidays.value.length === 0) return true
  const cellDate = formatCellDate(cell)
  const holiday = holidays.value.find(item => item.date === cellDate)
  return !holiday || !holiday.precipitation || Number(holiday.precipitation) <= 0
}
```

### 🎯 为什么内联样式有效

1. **最高优先级**: 内联样式的优先级最高，不会被任何CSS覆盖
2. **不受作用域限制**: 不受Vue的scoped样式限制
3. **不受Element Plus影响**: 不会被Element Plus的内部样式覆盖
4. **动态响应**: 可以根据数据状态动态改变样式

### 🎨 最终效果

- **有降雨的日期**: 淡蓝色背景 `rgba(64, 158, 255, 0.1)`，显示降雨量
- **无降雨的日期**: 灰色背景 `#f5f7fa`，灰色文字 `#c0c4cc`，不可点击
- **降雨量文字**: 蓝色 `#409eff`，粗体，12px字体
- **文字样式**: 16px，500字重，1.2行高

## 预期效果

- ✅ 有降雨的日期：淡蓝色背景，显示降雨量
- ✅ 无降雨的日期：灰色背景，不可选择
- ✅ 文字样式：与辅助决策页面完全一致
- ✅ 动态响应：根据数据状态实时更新样式
