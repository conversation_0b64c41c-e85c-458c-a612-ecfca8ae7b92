# 降雨日历Cell样式调试记录

## 问题描述
降雨日历的cell样式没有生效，需要参照辅助决策页面的样式进行复刻。

## 尝试的解决方案

### 1. 第一次尝试：Scoped样式 + :deep()
```scss
:deep(.cell) {
  padding: 5px 0;
  // ... 其他样式
}
```
**结果**: 样式可能没有生效，因为Element Plus组件的样式优先级较高。

### 2. 第二次尝试：在date-picker-container内部使用:deep()
```scss
.date-picker-container {
  :deep(.cell) {
    padding: 5px 0;
    // ... 其他样式
  }
}
```
**结果**: 更具体的选择器，但仍可能被Element Plus样式覆盖。

### 3. 第三次尝试：添加!important提高优先级
```scss
:deep(.cell) {
  padding: 5px 0 !important;
  display: flex !important;
  // ... 所有样式都加上!important
}
```
**结果**: 强制提高样式优先级。

### 4. 第四次尝试：全局样式
```scss
/* 在非scoped的style标签中 */
.cell {
  padding: 5px 0 !important;
  // ... 其他样式
}
```
**结果**: 全局样式，不受scoped限制，优先级最高。

## 当前实现

现在文件中同时包含了两套样式：

### A. Scoped样式（在date-picker-container内部）
```scss
.date-picker-container {
  :deep(.cell) {
    padding: 5px 0 !important;
    box-sizing: border-box !important;
    // ... 完整样式
  }
}
```

### B. 全局样式
```scss
.cell {
  padding: 5px 0 !important;
  box-sizing: border-box !important;
  // ... 完整样式
}
```

## 样式内容

### 基础cell样式
- padding: 5px 0
- display: flex, flex-direction: column
- align-items: center, justify-content: center

### has-rain样式（有降雨的日期）
- background-color: rgba(64, 158, 255, 0.1)
- border-radius: 4px

### disabled样式（禁用的日期）
- color: #c0c4cc
- background-color: #f5f7fa
- cursor: not-allowed
- 隐藏precipitation显示

### precipitation样式（降雨量文字）
- font-size: 12px
- color: #409eff
- font-weight: bold
- margin-top: 2px

## 验证方法

1. 打开浏览器开发者工具
2. 点击降雨日历标题打开日期选择器
3. 检查cell元素的样式是否正确应用
4. 查看有降雨的日期是否有蓝色背景
5. 查看降雨量文字是否正确显示

## 预期效果

- 有降雨的日期：淡蓝色背景，显示降雨量
- 无降雨的日期：灰色背景，不可选择
- 文字样式：与辅助决策页面完全一致
