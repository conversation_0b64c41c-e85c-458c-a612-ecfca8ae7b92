import request from '../utils/request'

// 端临气象
export function shortGet(params) {
  return request({
    url: `/taiyuan/jsdxx-all/stat`,
    method: 'get'
  })
}
export function jsdxxOnline(params) {
  return request({
    url: `/taiyuan/jsdxx-online/stat`,
    method: 'get'
  })
}
export function jsdxxOffline(params) {
  return request({
    url: `/taiyuan/jsdxx-offline/stat`,
    method: 'get'
  })
}
export function jsdxxRanking(params) {
  return request({
    url: `/taiyuan/jsdxx-ranking/stat`,
    method: 'get'
  })
}

// 泵站水
export function bzswGET() {
  return request({
    url: `/taiyuan/bzsw/stat`,
    method: 'get'
  })
}

export function gwyjGET() {
  return request({
    url: `/taiyuan/gwyj/stat`,
    method: 'get'
  })
}
export function wzdptjGET() {
  return request({
    url: `/taiyuan/wzdptj/stat`,
    method: 'get'
  })
}
export function bjxxGET() {
  return request({
    url: `/taiyuan/bjxx/stat`,
    method: 'get'
  })
}
export function bjsbxxlbGET() {
  return request({
    url: `/taiyuan/bjsbxxlb/stat`,
    method: 'get'
  })
}
export function ddzxgkGET() {
  return request({
    url: `/taiyuan/ddzxgk/stat`,
    method: 'get'
  })
}
export function ztzlGET() {
  return request({
    url: `/taiyuan/ztzl/stat`,
    method: 'get'
  })
}

export function caseInfoGET() {
  return request({
    url: `/taiyuan/caseInfo/stat`,
    method: 'get'
  })
}

export function dlqxGET() {
  return request({
    url: `/taiyuan/dlqx/stat`,
    method: 'get'
  })
}

export function zngzYldfl() {
  return request({
    url: `/taiyuan/zngz-yldfl/stat`,
    method: 'get'
  })
}
export function zngzBzyxzttj() {
  return request({
    url: `/taiyuan/zngz-bzyxzttj/stat`,
    method: 'get'
  })
}
export function zngzBzyxsjtj() {
  return request({
    url: `/taiyuan/zngz-bzyxsjtj/stat`,
    method: 'get'
  })
}
export function zngzBzqcswtj() {
  return request({
    url: `/taiyuan/zngz-bzqcswtj/stat`,
    method: 'get'
  })
}

export function zngzBzcpltj() {
  return request({
    url: `/taiyuan/zngz-bzcpltj/stat`,
    method: 'get'
  })
}

export function zngzBzsjxx() {
  return request({
    url: `/taiyuan/zngz-bzsjxx/stat`,
    method: 'get'
  })
}
export function bjsbxxlb() {
  return request({
    url: `/taiyuan/bjsbxxlb/stat`,
    method: 'get'
  })
}
export function ddzxgk() {
  return request({
    url: `/taiyuan/ddzxgk/stat`,
    method: 'get'
  })
}

export function zngzBjlyfl() {
  return request({
    url: `/taiyuan/zngz-bjlyfl/stat`,
    method: 'get'
  })
}

export function zngzYjwz() {
  return request({
    url: `/taiyuan/zngz-yjwz/stat`,
    method: 'get'
  })
}
export function fxnl() {
  return request({
    url: `/taiyuan/fxnl/stat`,
    method: 'get'
  })
}
export function yqxxget() {
  return request({
    url: `/taiyuan/yqxx/stat`,
    method: 'get'
  })
}

export function yqxxgetPage(pageNum = 1, pageSize = 20) {
  return request({
    url: `/taiyuan/yqxxb/list?pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'get'
  })
}
export function zngzYldtj() {
  return request({
    url: `/taiyuan/zngz-yldtj/stat`,
    method: 'get'
  })
}
export function zngzSwbjstj() {
  return request({
    url: `/taiyuan/zngz-swbjstj/stat`,
    method: 'get'
  })
}
export function zngzBjtj(name = '') {
  return request({
    url: `/taiyuan/zngz-bjtj/stat?name=` + name,
    method: 'get'
  })
}

export function yingjiwuziTable(wzTypeName = '', cbdId = '', pageNum = 1, pageSize = 10) {
  return request({
    url: `/taiyuan/wz/list?wzlx=${wzTypeName}&cbdId=${cbdId}&pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'get'
  })
}
export function yingjiwuziRootTable(cbdId = '', pageNum = 1, pageSize = 10) {
  return request({
    url: `/taiyuan/wz/list?cbdId=${cbdId}&pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'get'
  })
}

export function wzwzlxGET(wzTypeName = '') {
  return request({
    url: `/taiyuan/wz/list?wzlx=${wzTypeName}&pageSize=10000`,
    method: 'get'
  })
}

export function yjcbdTable() {
  return request({
    url: `/taiyuan/yjcbd/list`,
    method: 'get'
  })
}

export function caseInformationPage(pageNum = 1, pageSize = 20, eventGridName = '', rptTime = '') {
  return request({
    url: `/taiyuan/caseInformation/list?pageNum=${pageNum}&pageSize=${pageSize}&eventGridName=${eventGridName}&rptTime=${rptTime}`,
    method: 'get'
  })
}
export function fkclxxbGET(pageNum = 1, pageSize = 20, cphm = '', jsrxm = '') {
  return request({
    url: `/taiyuan/fkclxxb/list?pageNum=${pageNum}&pageSize=${pageSize}&cphm=${cphm}&jsrxm=${jsrxm}`,
    method: 'get'
  })
}
export function bzxxbGET(pageNum = 1, pageSize = 20, name = '') {
  return request({
    url: `/taiyuan/bzxxb/list?pageNum=${pageNum}&pageSize=${pageSize}&name=${name}`,
    method: 'get'
  })
}
export function bzxxbGETcode(sbcode = '') {
  return request({
    url: `/taiyuan/bzxxb/list?sbcode=${sbcode}`,
    method: 'get'
  })
}
export function cstqGET() {
  return request({
    url: `/taiyuan/cstq/stat`,
    method: 'get'
  })
}

// 2
export function zngzYldbjNEW() {
  return request({
    url: `/taiyuan/zngz-yldbj/stat`,
    method: 'get'
  })
}

export function statisticsNEW(type = '', cate = '', grade = '') {
  return request({
    url: `/taiyuan/statistics/list?pageSize=10000&type=${type}&cate=${cate}&grade=${grade}`,
    method: 'get'
  })
}

export function statisticsNEWdialog(pageNum, pageSize, type = '') {
  return request({
    url: `/taiyuan/statistics/list?pageNum=${pageNum}&pageSize=${pageSize}&type=${type}`,
    method: 'get'
  })
}

export function statisticsNEWdialogWithParams(params) {
  let url = `/taiyuan/statistics/list?pageNum=${params.pageNum}&pageSize=${params.pageSize}`

  // 添加查询条件
  if (params.area) {
    url += `&area=${encodeURIComponent(params.area)}`
  }
  if (params.type) {
    url += `&type=${encodeURIComponent(params.type)}`
  }

  return request({
    url,
    method: 'get'
  })
}

export function zngzPsssbjNEW() {
  return request({
    url: `/taiyuan/zngz-psssbj/stat`,
    method: 'get'
  })
}
export function zngzNzyxtjNEW() {
  return request({
    url: `/taiyuan/zngz-bzyxtj/stat`,
    method: 'get'
  })
}
export function zngzBzsjxxNEW() {
  return request({
    url: `/taiyuan/zngz-bzsjxx/stat`,
    method: 'get'
  })
}

export function zngzFxnlNEW() {
  return request({
    url: `/taiyuan/zngz-fxnl/stat`,
    method: 'get'
  })
}
export function zngzFdzltjNEW() {
  return request({
    url: `/taiyuan/zngz-ddzltj/stat`,
    method: 'get'
  })
}
export function zngzFdzltj2NEW(type) {
  return request({
    url: `/taiyuan/zngz-ddzltj2/stat?name=` + type,
    method: 'get'
  })
}
export function zngzDdlbtjNEW() {
  return request({
    url: `/taiyuan/zngz-ddlbtj/stat`,
    method: 'get'
  })
}
export function caseInfoNEW() {
  return request({
    url: `/taiyuan/caseInfo/stat`,
    method: 'get'
  })
}

export function getLocationsingle(params) {
  return request({
    url: `/taiyuan/yldsbxx/list?pageSize=${params.pageSize}&sstype=${params.sstype}&sbname=${params.sbname}`,
    method: 'get'
  })
}

export function yjdwGETperson(pageNum, pageSize, leader, leaderPhone) {
  return request({
    url: `/taiyuan/yjdw/list?pageSize=${pageSize}&pageNum=${pageNum}&leader=${leader}&leaderPhone=${leaderPhone}`,
    method: 'get'
  })
}
