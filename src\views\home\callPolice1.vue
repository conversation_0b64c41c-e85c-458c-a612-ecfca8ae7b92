<template>
  <div class="screen-container">
    <div class="bg-layer bg-layer-1"></div>
    <div class="bg-layer bg-layer-2"></div>
    <div class="bg-layer bg-layer-3"></div>
    <!-- 地图容器，修改ID避免重复 -->
    <!-- <div id="player" class="map-container"></div> -->

    <!-- 左侧区域容器 -->
    <div class="left-container">
      <div class="left-section">
        <!-- <div class="time-weather">
          <div class="time-date">
            <div class="time">{{ currentTime }}</div>
            <div class="date">{{ currentDate }}</div>
          </div>
          <div class="divider"></div>
          <div class="weather">
            <img class="weather-icon" :src="getAssetsFile('sunny.png')" alt="天气" />
            <img class="weather-icon1" :src="getAssetsFile('wendu.png')" alt="温度" />
            <span class="temperature">17℃</span>
          </div>
        </div> -->

        <!-- 上下布局容器 -->
        <div class="left-content">
          <!-- 第一部分 -->

          <div class="content-layout-first">
            <div class="content-layout-header">
              <div class="weather-info-bar">
                <div class="current-weather">
                  <span class="weather-label">当前天气</span>
                  <div class="city-info">
                    <img class="location-icon" src="@/assets/images/home/<USER>" alt="" />
                    <span class="city-name"
                      >{{ weaherData.city }} &nbsp;&nbsp;{{ weaherData.minTemperature }}~{{
                        weaherData.maxTemperature
                      }}</span
                    >
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="weather-detail">
                  <div class="weather-icon-large">
                    <img src="@/assets/images/home/<USER>" alt="大雨" />
                    <div>{{ weaherData.daytimeWind }}，{{ weaherData.daytimeWeather }}</div>
                  </div>
                  <div class="weather-icon-large">
                    <img src="@/assets/images/home/<USER>" alt="大雨" />
                    <div>{{ weaherData.nighttimeWind }}，{{ weaherData.nighttimeWeather }}</div>
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />
                <div class="weather-forecast">
                  <div class="forecast-item">
                    <div class="forecast-title">未来24h天气</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">{{ weaherData.forecast_24h }}</span>
                      <!-- <span class="forecast-value-unit">mm</span> -->
                    </div>
                  </div>
                  <div class="forecast-item">
                    <div class="forecast-title">检测次数</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">{{ weaherData.monitor_count }}</span>
                      <!-- <span class="forecast-value-unit">mm</span> -->
                    </div>
                  </div>
                  <div class="forecast-item">
                    <div class="forecast-title">预警等级</div>
                    <div class="forecast-value1">
                      <span class="forecast-value-num">{{ weaherData.monitor_level }}</span>
                      <!-- <span class="forecast-value-unit">mm</span> -->
                    </div>
                  </div>
                </div>
                <img class="divider-line" src="@/assets/images/home/<USER>" alt="分隔线" />

                <div class="publish-info">
                  <span class="publish-label" @click="yuntuClick">云图</span>
                  <span class="publish-label" @click="yuzhongkuaibao">雨中快报</span>
                  <!-- <div class="publish-times"> -->
                  <!-- <div class="time-item">开始时间: 20:50:00</div> -->
                  <!-- <div class="time-item">结束时间: 18:00:00</div> -->
                  <!-- </div> -->
                </div>
              </div>
            </div>

            <div class="call-wrap">
              <div class="call-left-wrap new-add-border">
                <div class="call-left">
                  <div class="call-yuyan">
                    <div class="chart-headers touchang">
                      <div class="chart-title">预演方案</div>
                      <!-- <div class="alarm-tabs"></div> -->
                      <!-- <div class="title-more" @click="yuzhongkuaibao">雨中快报</div> -->
                    </div>
                    <div class="yuyan-content">
                      <div class="yuyan-c-left">
                        <div class="yuyan-c-l-input">
                          <div class="input-text">雨量</div>
                          <el-select
                            popper-class="call-selectp"
                            @change="yuChange"
                            v-model="valueyuliang"
                            class="m-2"
                            placeholder="请选择"
                            size="large"
                            style="width: 140px"
                          >
                            <el-option v-for="item in rainSelectDatas" :key="item" :label="item" :value="item" />
                          </el-select>
                          <el-button type="warning" size="large" style="font-size: 20px">预报等级: Ⅳ</el-button>
                        </div>
                        <div class="yuyan-c-l-t">方案描述</div>
                        <!-- <div class="yuyan-c-l-b">预计时间：{{ yuyanData?.description?.expected_time }}</div> -->
                        <div class="yuyan-c-l-b">模拟时长：{{ yuyanData?.description?.estimated_duration }}</div>
                        <div class="yuyan-c-l-b">场景说明：{{ yuyanData?.description?.scene_description }}</div>
                      </div>
                      <div class="yuyan-c-right">
                        <!-- <div class="yuyan-c-r-item" v-for="(item, index) in 4">
                        <div class="yuyan-c-r-t">积水面积(㎡)</div>
                        <div class="yuyan-c-r-bottom" :class="['yuyan-c-r-i' + index]">89.11</div>
                      </div> -->

                        <div class="yuyan-c-r-item">
                          <div class="yuyan-c-r-t">积水面积(k㎡)</div>
                          <div class="yuyan-c-r-bottom yuyan-c-r-i0">{{ yuyanData.accumulated_area }}</div>
                        </div>
                        <div class="yuyan-c-r-item">
                          <div class="yuyan-c-r-t">最大积水水深(m)</div>
                          <div class="yuyan-c-r-bottom yuyan-c-r-i1">{{ yuyanData.max_water_depth }}</div>
                        </div>
                        <div class="yuyan-c-r-item">
                          <div class="yuyan-c-r-t">最大积水水量(m³)</div>
                          <div class="yuyan-c-r-bottom yuyan-c-r-i2">{{ yuyanData.water_volume }}</div>
                        </div>
                        <div class="yuyan-c-r-item">
                          <div class="yuyan-c-r-t">最大积水时间(min)</div>
                          <div class="yuyan-c-r-bottom yuyan-c-r-i3">{{ yuyanData.accumulation_time }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="call-neilao">
                  <div class="chart-headers touchang">
                    <div class="chart-title">内涝信息统计</div>
                    <div class="alarm-tabs"></div>
                  </div>
                  <div class="rain-monitor-table-container-neilao">
                    <div class="rain-monitor-table-header">
                      <div class="rain-th th-index1">名称</div>
                      <div class="rain-th th-station1">类型</div>
                      <div class="rain-th th-hour1">积水面积</div>
                      <div class="rain-th th-warning1">最大积水水深</div>
                      <div class="rain-th th-warning2">积水水量(m³)</div>
                      <div class="rain-th th-warning3">积水时间(h)</div>
                      <div class="rain-th th-warning3">等级</div>
                    </div>
                    <div class="rain-monitor-table-body">
                      <div class="rain-tr" @click="neilaoclick(row)" v-for="(row, idx) in nlxxtjData" :key="row.index">
                        <div class="rain-td th-index1">{{ row.name }}</div>
                        <div class="rain-td th-station1">{{ row.type }}</div>
                        <div class="rain-td th-hour1">{{ row.accumulated_area }}</div>
                        <div class="rain-td th-warning1">{{ row.max_water_depth }}</div>
                        <div class="rain-td th-warning2">{{ row.water_volume }}</div>
                        <div class="rain-td th-warning3">{{ row.accumulation_time }}</div>
                        <div
                          class="rain-td th-warning3"
                          :style="{
                            color:
                              row.level === 'Ⅰ'
                                ? 'red'
                                : row.level === 'Ⅱ'
                                  ? 'orange'
                                  : row.level === 'Ⅲ'
                                    ? 'yellow'
                                    : 'blue'
                          }"
                        >
                          {{ row.level }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="call-charts" ref="callCharts"></div>
                </div> -->
                </div>
                <!-- <div class="call-middle">
                <div class="chart-headers">
                  <div class="chart-title">全市雨量情况</div>
                  <div class="alarm-tabs"></div>
                </div>
                <div class="call-yuliang"></div>
                <div class="chart-headers">
                  <div class="chart-title">管网预警</div>
                  <div class="alarm-tabs">
                    <div class="alarm-tab">区/县/街道</div>
                  </div>
                </div>
                <div class="guanwang-baojing">
                  <div class="guanwang-wrap" v-for="gItem in gwyjDATA">
                    <div class="guanwang-item">
                      <div class="guanwang-item-value">{{ gItem.value }}</div>
                      <div class="guanwang-item-val">{{ gItem.online }}/{{ gItem.total }}</div>
                    </div>
                    <div class="guanwang-text">{{ gItem.name }}</div>
                  </div>
                </div>
                <div style="height: 60px"></div>
                <div class="chart-headers">
                  <div class="chart-title">报警信息列表</div>
                  <div class="alarm-tabs"></div>
                </div>
                <div class="rain-monitor-table-container-middle">
                  <div class="rain-monitor-table-header">
                    <div class="rain-th th-index1">序号</div>
                    <div class="rain-th th-station1">点位信息</div>
                    <div class="rain-th th-hour1">报警时间</div>
                    <div class="rain-th th-warning1">位置</div>
                    <div class="rain-th th-warning2">阈值</div>
                  </div>
                  <div class="rain-monitor-table-body">
                    <div class="rain-tr" v-for="(row, idx) in bengzhanshuiwei" :key="row.index">
                      <div class="rain-td th-index1">{{ row.name }}</div>
                      <div class="rain-td th-station1">{{ row.type }}</div>
                      <div class="rain-td th-hour1">{{ row.deep }}</div>
                      <div class="rain-td th-warning1">{{ row.range }}</div>
                      <div class="rain-td th-warning2">{{ row.time }}</div>
                    </div>
                  </div>
                </div>
              </div> -->
                <div class="call-right">
                  <!-- <div class="c-r-i">
                  <div class="chart-headers touchang">
                    <div class="chart-title">全市雨量情况</div>
                    <div class="alarm-tabs"></div>
                  </div>
                  <div class="call-neilao-mianji">
                    <div class="c-n-m-item">
                      <div class="c-n-m-i-img"></div>
                      <div class="c-n-m-i-text">
                        <div class="c-n-m-i-v">{{ qsyltjDATA.forecast_2h }}</div>
                        <div class="c-n-m-i-t">未来2h</div>
                      </div>
                    </div>
                    <div class="c-n-m-item">
                      <div class="c-n-m-i-img"></div>
                      <div class="c-n-m-i-text">
                        <div class="c-n-m-i-v">{{ qsyltjDATA.cumulative_4h }}</div>
                        <div class="c-n-m-i-t">4小时累计</div>
                      </div>
                    </div>
                    <div class="c-n-m-item">
                      <div class="c-n-m-i-img"></div>
                      <div class="c-n-m-i-text">
                        <div class="c-n-m-i-v">{{ qsyltjDATA.cumulative_6h }}</div>
                        <div class="c-n-m-i-t">6小时累计</div>
                      </div>
                    </div>
                    <div class="c-n-m-item">
                      <div class="c-n-m-i-img"></div>
                      <div class="c-n-m-i-text">
                        <div class="c-n-m-i-v">{{ qsyltjDATA.cumulative_12h }}</div>
                        <div class="c-n-m-i-t">12小时累计</div>
                      </div>
                    </div>
                    <div class="c-n-m-item">
                      <div class="c-n-m-i-img"></div>
                      <div class="c-n-m-i-text">
                        <div class="c-n-m-i-v">{{ qsyltjDATA.cumulative_24h }}</div>
                        <div class="c-n-m-i-t">24小时累计</div>
                      </div>
                    </div>
                    <div class="c-n-m-item">
                      <div class="c-n-m-i-img"></div>
                      <div class="c-n-m-i-text">
                        <div class="c-n-m-i-v">{{ qsyltjDATA.cumulative_72h }}</div>
                        <div class="c-n-m-i-t">72小时累计</div>
                      </div>
                    </div>
                  </div>
                </div> -->

                  <div class="n-l-f">
                    <div class="chart-headers touchang">
                      <div class="chart-title">内涝发生情况分析</div>
                      <div class="alarm-tabs"></div>
                    </div>
                    <div class="neilao-fasheng">
                      <!-- <div class="neilao-small">内涝实时面积</div>
                    <div class="neilao-shishi">
                      <div class="n-s-item">
                        <div class="n-s-i-left">
                          <div class="n-s-i-l-text">低风险</div>
                          <div class="n-s-i-l-bold">{{ nlfsqkfxDATA.current_risk_areas?.low_risk?.current }}</div>
                          <div class="n-s-i-l-img" v-if="nlfsqkfxDATA.current_risk_areas?.low_risk?.change > 0"></div>
                          <div class="n-s-i-l-img-low" v-else></div>
                          <div class="n-s-i-l-xiao">{{ nlfsqkfxDATA.current_risk_areas?.low_risk?.change }}</div>
                        </div>
                        <div class="n-s-i-left">
                          <div class="n-s-i-l-text">低风险</div>
                          <div class="n-s-i-l-bold">{{ nlfsqkfxDATA.current_risk_areas?.low_risk?.current }}</div>
                          <div class="n-s-i-l-img" v-if="nlfsqkfxDATA.current_risk_areas?.low_risk?.change > 0"></div>
                          <div class="n-s-i-l-img-low" v-else></div>
                          <div class="n-s-i-l-xiao">{{ nlfsqkfxDATA.current_risk_areas?.low_risk?.change }}</div>
                        </div>
                        <div class="n-s-i-line"></div>
                      </div>
                      <div class="n-s-item">
                        <div class="n-s-i-left">
                          <div class="n-s-i-l-text">中风险</div>
                          <div class="n-s-i-l-bold">{{ nlfsqkfxDATA.current_risk_areas?.medium_risk?.current }}</div>
                          <div
                            class="n-s-i-l-img"
                            v-if="nlfsqkfxDATA.current_risk_areas?.medium_risk?.change > 0"
                          ></div>
                          <div class="n-s-i-l-img-low" v-else></div>
                          <div class="n-s-i-l-xiao">{{ nlfsqkfxDATA.current_risk_areas?.medium_risk?.change }}</div>
                        </div>
                        <div class="n-s-i-left">
                          <div class="n-s-i-l-text">中风险</div>
                          <div class="n-s-i-l-bold">{{ nlfsqkfxDATA.current_risk_areas?.medium_risk?.current }}</div>
                          <div
                            class="n-s-i-l-img"
                            v-if="nlfsqkfxDATA.current_risk_areas?.medium_risk?.change > 0"
                          ></div>
                          <div class="n-s-i-l-img-low" v-else></div>
                          <div class="n-s-i-l-xiao">{{ nlfsqkfxDATA.current_risk_areas?.medium_risk?.change }}</div>
                        </div>
                        <div class="n-s-i-line"></div>
                      </div>
                      <div class="n-s-item">
                        <div class="n-s-i-left">
                          <div class="n-s-i-l-text">高风险</div>
                          <div class="n-s-i-l-bold">{{ nlfsqkfxDATA.current_risk_areas?.high_risk?.current }}</div>
                          <div class="n-s-i-l-img" v-if="nlfsqkfxDATA.current_risk_areas?.high_risk?.change > 0"></div>
                          <div class="n-s-i-l-img-low" v-else></div>
                          <div class="n-s-i-l-xiao">{{ nlfsqkfxDATA.current_risk_areas?.high_risk?.change }}</div>
                        </div>
                        <div class="n-s-i-left">
                          <div class="n-s-i-l-text">高风险</div>
                          <div class="n-s-i-l-bold">{{ nlfsqkfxDATA.current_risk_areas?.high_risk?.current }}</div>
                          <div class="n-s-i-l-img" v-if="nlfsqkfxDATA.current_risk_areas?.high_risk?.change > 0"></div>
                          <div class="n-s-i-l-img-low" v-else></div>
                          <div class="n-s-i-l-xiao">{{ nlfsqkfxDATA.current_risk_areas?.high_risk?.change }}</div>
                        </div>
                        <div class="n-s-i-line"></div>
                      </div>
                      <div class="n-s-bottom">
                        <div class="n-s-b-t">较上一时刻</div>
                        <div class="n-s-b-t">较上一小时</div>
                      </div>
                    </div> -->
                      <div class="n-s-wrap-bottom">
                        <div class="n-s-w-b-item">
                          <div class="neilao-small">各级面积占比</div>
                          <div class="n-s-w-b-i-pie" ref="pieLeft"></div>
                        </div>
                        <div class="n-s-w-b-item">
                          <div class="neilao-small">各区内涝面积统计(km²)</div>
                          <!-- <div class="n-s-w-b-i-pie" ref="pieRight"></div> -->
                          <div class="n-s-w-b-i-pie" ref="fxzb"></div>
                        </div>
                      </div>
                      <!-- <div class="rain-monitor-table-container-mianji">
                    <div class="rain-monitor-table-header">
                      <div class="rain-th th-index1">序号</div>
                      <div class="rain-th th-station1">站名</div>
                      <div class="rain-th th-hour1">时刻(min)</div>
                      <div class="rain-th th-warning1">积水变化(m²)</div>
                      <div class="rain-th th-warning2">面积扩展(m²)</div>
                      <div class="rain-th th-warning3">持续时间(min)</div>
                    </div>
                    <div class="rain-monitor-table-body">
                      <div class="rain-tr" v-for="(row, idx) in bengzhanshuiwei" :key="row.index">
                        <div class="rain-td th-index1">{{ row.name }}</div>
                        <div class="rain-td th-station1">{{ row.type }}</div>
                        <div class="rain-td th-hour1">{{ row.deep }}</div>
                        <div class="rain-td th-warning1">{{ row.range }}</div>
                        <div class="rain-td th-warning2">{{ row.time }}</div>
                        <div class="rain-td th-warning3">{{ row.class }}</div>
                      </div>
                    </div>
                  </div> -->
                      <!-- <div class="chart-headers">
                    <div class="chart-title">各级风险面积占比</div>
                    <div class="alarm-tabs"></div>
                  </div> -->
                      <!-- <div class="call-right-pie">
                    <div class="pie-left" ref="pieLeft"></div>
                    <div class="pie-right" ref="pieRight"></div>
                  </div> -->
                    </div>
                  </div>
                </div>

                <div class="leftbei"></div>
                <div class="righttbei"></div>
                <div class="bottombeibg"></div>
              </div>
              <div class="call-left-w-bottom new-add-border">
                <div class="chart-headers touchang">
                  <div class="chart-title">预报积水点信息</div>
                  <!-- <div class="alarm-tabs"></div> -->
                  <!-- <div class="title-more" @click="yuzhongkuaibao">雨中快报</div> -->
                </div>
                <div class="rain-monitor-table-container-neilao">
                  <div class="rain-monitor-table-header">
                    <div class="rain-th th-index1">位置名称</div>
                    <div class="rain-th th-station1">积水面积(m²)</div>
                    <div class="rain-th th-hour1">积水深度(m)</div>
                    <div class="rain-th th-warning1">积水时间(min)</div>
                    <div class="rain-th th-warning2">影响道路</div>
                    <div class="rain-th th-warning3">影响小区</div>
                    <div class="rain-th th-warning3">预警等级</div>
                    <div class="rain-th th-warning3">预警时间</div>
                    <div class="rain-th th-warning3">推荐方案</div>
                    <div class="rain-th th-warning3">推荐应急排放点</div>
                  </div>
                  <div class="rain-monitor-table-body">
                    <div class="rain-tr" @click="neilaoclick(row)" v-for="(row, idx) in nlxxtjData" :key="row.index">
                      <div class="rain-td th-index1">{{ row.name }}</div>
                      <div class="rain-td th-station1">{{ row.waterArea }}</div>
                      <div class="rain-td th-hour1">{{ row.waterDepth }}</div>
                      <div class="rain-td th-warning1">{{ row.waterTime }}</div>
                      <div class="rain-td th-warning2">{{ row.affectedRoadLength }}</div>
                      <div class="rain-td th-warning3">{{ row.affectedVillage }}</div>
                      <div class="rain-td th-warning3" style="display: flex;justify-content: center">
                        <div style="color: red" v-if="row.alarmLevel === 'I'">{{ row.alarmLevel }}</div>
                        <div style="color: orange" v-if="row.alarmLevel === 'II'">{{ row.alarmLevel }}</div>
                        <div style="color: yellow" v-if="row.alarmLevel === 'III'">{{ row.alarmLevel }}</div>
                        <div style="color: blue" v-if="row.alarmLevel === 'IV'">{{ row.alarmLevel }}</div>
                        <img style="position: relative;left: 12px;" v-if="row.isIncrease === 'true'" src="@/assets/images/home/<USER>" width="20" height="32" alt="">
                      </div>
                      <div class="rain-td th-warning3">{{ row.forecastTime }}</div>
                      <div class="rain-td th-warning3">{{ row.recommendedPlan }}</div>
                      <div class="rain-td th-warning3">{{ row.dischargePoint }}</div>
                      <!-- <div
                        class="rain-td th-warning3"
                        :style="{
                          color:
                            row.level === 'Ⅰ'
                              ? 'red'
                              : row.level === 'Ⅱ'
                                ? 'orange'
                                : row.level === 'Ⅲ'
                                  ? 'yellow'
                                  : 'blue'
                        }"
                      >
                        {{ row.level }}
                      </div> -->
                    </div>
                  </div>
                </div>
                <div class="leftbei"></div>
                <div class="righttbei"></div>
                <div class="bottombeibg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间区域容器 -->
    <div class="middle-container">
      <div class="middle-section">
        <!-- 标题 -->
        <!-- <div class="section-title">内涝安全预警监测综合驾驶舱系统</div> -->
        <!-- 导航按钮 -->
        <!-- <div class="nav-buttons">
          <div
            v-for="(btn, index) in navButtons"
            :key="index"
            :class="['nav-button', { active: activeNavButton === index }]"
            @click="activeNavButton = index"
          >
            <div class="nav-button-text">{{ btn.text }}</div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 右侧区域容器 -->
    <div class="right-container">
      <div class="right-section">
        <!-- 上部分：用户信息和返回门户 -->
        <!-- <div class="user-portal-container">
          <div class="user-info">
            <img class="user-icon" src="https://picsum.photos/200/300" alt="用户" />
            <span class="username">管理员</span>
          </div>
          <div class="portal-back">
            <img class="portal-icon" src="https://picsum.photos/200/300" alt="门户" />
            <span class="portal-text">返回门户</span>
          </div>
        </div> -->

        <!-- 下部分：新增的内容区域 -->
        <div class="right-content">
          <div class="call-right-two">
            <div class="c-r-t-top">
              <div class="c-r-t-t-l-right">
                <div class="c-r-t-t-l-r-wrap">
                  <div class="c-r-t-bottom new-add-border">
                    <div class="c-r-t-cc">
                      <div
                        class="c-r-t-cc-i"
                        :class="{ 'cc-i-active': paihongquFlag === '1' }"
                        @click="paihongquClick('1')"
                      >
                        <div class="c-r-t-cc-t-title">泵站</div>
                        <div class="c-r-t-cc-t">排水能力不达标个数</div>
                        <div class="c-r-t-cc-bb">{{ sbdbqkDATA.nsrtc }}</div>
                      </div>
                      <div
                        class="c-r-t-cc-i"
                        :class="{ 'cc-i-active': paihongquFlag === '2' }"
                        @click="paihongquClick('2')"
                      >
                        <div class="c-r-t-cc-t-title">排洪渠</div>
                        <div class="c-r-t-cc-t">溢堤长度</div>
                        <div class="c-r-t-cc-bb">{{ sbdbqkDATA.odl }}</div>
                      </div>
                      <div
                        class="c-r-t-cc-i"
                        :class="{ 'cc-i-active': paihongquFlag === '3' }"
                        @click="paihongquClick('3')"
                      >
                        <div class="c-r-t-cc-t-title">缓洪池</div>
                        <div class="c-r-t-cc-t">排水能力不达标个数</div>
                        <div class="c-r-t-cc-bb">{{ sbdbqkDATA.efpc }}</div>
                      </div>
                    </div>
                    <div class="rain-monitor-table-container-tongji1">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-index1" v-for="data in bengzhanshuiweiHEADER">{{ data }}</div>
                        <!-- <div class="rain-th th-station1">长度</div> -->
                        <!-- <div class="rain-th th-hour1">超标水位(m)</div> -->
                      </div>
                      <div class="rain-monitor-table-body" v-if="paihongquFlag === '1'">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bengzhanshuiwei"
                          :key="row.index"
                          @click="singleGetDataClick(row)"
                        >
                          <div class="rain-td th-index1">{{ row.name }}</div>
                          <div class="rain-td th-station1">{{ row.design_ability }}</div>
                          <div class="rain-td th-hour1">{{ row.excessive_ability }}</div>
                        </div>
                      </div>
                      <div class="rain-monitor-table-body" v-if="paihongquFlag === '2'">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bengzhanshuiwei"
                          :key="row.index"
                          @click="singleGetDataClick(row)"
                        >
                          <div class="rain-td th-index1">{{ row.name }}</div>
                          <div class="rain-td th-station1">{{ row.length }}</div>
                          <div class="rain-td th-hour1">{{ row.water_level }}</div>
                        </div>
                      </div>
                      <div class="rain-monitor-table-body" v-if="paihongquFlag === '3'">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bengzhanshuiwei"
                          :key="row.index"
                          @click="singleGetDataClick(row)"
                        >
                          <div class="rain-td th-index1">{{ row.name }}</div>
                          <div class="rain-td th-station1">{{ row.scale }}</div>
                          <div class="rain-td th-hour1">{{ row.excessive_scale }}</div>
                        </div>
                      </div>
                      <!-- <div class="rain-monitor-table-body" v-if="paihongquFlag === '4'">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bengzhanshuiwei"
                          :key="row.index"
                          @click="singleClick(row)"
                        >
                          <div class="rain-td th-index1">{{ row.name }}</div>
                          <div class="rain-td th-station1">{{ row.design_ability }}</div>
                          <div class="rain-td th-hour1">{{ row.excessive_ability }}</div>
                        </div>
                      </div> -->
                      <!-- <div class="rain-monitor-table-body" v-if="paihongquFlag === '5'">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bengzhanshuiwei"
                          :key="row.index"
                          @click="singleClick(row)"
                        >
                          <div class="rain-td th-index1">{{ row.name }}</div>
                          <div class="rain-td th-station1">{{ row.value }}</div>
                        </div>
                      </div> -->
                    </div>

                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg" style="left:27%"></div>
                  </div>
                  <div class="c-r-t-t-l-r-w-right new-add-border">
                    <div class="chart-headers touchang">
                      <div class="chart-title">设施调度</div>
                      <div class="alarm-tabs"></div>
                    </div>
                    <div class="rain-monitor-table-container-neilao-r-wrap">
                      <div class="rain-monitor-table-header">
                        <div class="rain-th th-station1">设施名称</div>
                        <div class="rain-th th-hour1">控制要求</div>
                        <div class="rain-th th-warning1">开启时间</div>
                        <div class="rain-th th-warning2">推送状态</div>
                      </div>
                      <div class="rain-monitor-table-body">
                        <div
                          class="rain-tr"
                          v-for="(row, idx) in bjyafqdbqkdata"
                          :key="row.index"
                          @click="setMarkers(row)"
                        >
                          <div class="rain-td th-station1">{{ row.name }}</div>
                          <div class="rain-td th-hour1">{{ row.control_requirement }}</div>
                          <div class="rain-td th-warning1">{{ row.start_time }}</div>
                          <div class="rain-td th-warning2" style="color: yellow">{{ row.push_status }}</div>
                        </div>
                      </div>
                    </div>

                    <div class="leftbei"></div>
                    <div class="righttbei"></div>
                    <div class="bottombeibg"></div>
                  </div>
                </div>
                <!-- <div class="c-r-right-top">
                  <div class="c-r-t-item">
                    <div class="neilao-small">排洪渠</div>
                    <div class="c-r-t-i-bottom">
                      <div class="c-r-t-i-b-d">{{ sbdbqkDATA.odl }}</div>
                      <div class="b-d-name">溢堤长度</div>
                    </div>
                  </div>
                  <div class="c-r-t-item">
                    <div class="neilao-small">缓洪池</div>
                    <div class="c-r-t-i-bottom">
                      <div class="c-r-t-i-b-d">{{ sbdbqkDATA.efpc }}</div>
                      <div class="b-d-name">排水能力不达标个数</div>
                    </div>
                  </div>
                  <div class="c-r-t-item">
                    <div class="neilao-small">管网</div>
                    <div class="c-r-t-i-bottom">
                      <div class="c-r-t-i-b-d">{{ sbdbqkDATA.nsdcc }}</div>
                      <div class="b-d-name">缓洪时间不达标个数</div>
                    </div>
                  </div>
                  <div class="c-r-t-item">
                    <div class="neilao-small">泵站</div>
                    <div class="c-r-t-i-bottom">
                      <div class="c-r-t-i-b-d">{{ sbdbqkDATA.nsrtc }}</div>
                      <div class="b-d-name">排水能力不达标个数</div>
                    </div>
                  </div>
                  <div class="c-r-t-item">
                    <div class="neilao-small">检流井</div>
                    <div class="c-r-t-i-bottom">
                      <div class="c-r-t-i-b-d">{{ sbdbqkDATA.psnsdcc }}</div>
                      <div class="b-d-name">溢流个数</div>
                    </div>
                  </div>
                </div> -->
                <!-- <div class="paishuinengli" ref="pashuineng"></div> -->

                <div class="yingxiangfanwei">
                  <div class="chart-headers touchang">
                    <div class="chart-title">预案调度指令集</div>
                    <div class="alarm-tabs"></div>
                  </div>
                  <div style="height: 14px"></div>
                  <div class="y-x-f-f-w-w">
                    <div class="fanwei-left new-add-border">
                      <div class="neilao-small" style="position: relative; left: 22px">人员调度</div>
                      <div class="rain-monitor-table-container-neilao-right">
                        <div class="rain-monitor-table-header">
                          <div class="rain-th th-station1">单位名称</div>
                          <div class="rain-th th-hour1">队伍名称</div>
                          <div class="rain-th th-warning1">处理对象</div>
                          <div class="rain-th th-warning2">联系电话</div>
                          <div class="rain-th th-warning3">推送状态</div>
                        </div>
                        <div class="rain-monitor-table-body">
                          <div
                            class="rain-tr"
                            v-for="(row, idx) in renyuandiaodu"
                            :key="row.index"
                            @click="setMarkers(row)"
                          >
                            <div class="rain-td th-station1">{{ row.unit_name }}</div>
                            <div class="rain-td th-hour1">{{ row.team_name }}</div>
                            <div class="rain-td th-warning1">{{ row.processing_object }}</div>
                            <div class="rain-td th-warning2">{{ row.contact_phone_new }}</div>
                            <div class="rain-td th-warning3" style="color: yellow">{{ row.push_status }}</div>
                          </div>
                        </div>
                      </div>

                      <div class="leftbei"></div>
                      <div class="righttbei"></div>
                      <div class="bottombeibg bottom-left"></div>
                    </div>

                    <div class="fanwei-middle new-add-border">
                      <div class="neilao-small">车辆调度</div>
                      <div class="rain-monitor-table-container-neilao-right-b">
                        <div class="rain-monitor-table-header">
                          <div class="rain-th th-index1">单位名称</div>
                          <div class="rain-th th-station1">车辆编号</div>
                          <div class="rain-th th-hour1">处理对象</div>
                          <div class="rain-th th-warning1">联系电话</div>
                          <div class="rain-th th-warning2">推送状态</div>
                        </div>
                        <div class="rain-monitor-table-body">
                          <div class="rain-tr" v-for="(row, idx) in cheliangdiaodu" :key="row.index">
                            <div class="rain-td th-index1">{{ row.unit_name }}</div>
                            <div class="rain-td th-station1">{{ row.vehicle_number }}</div>
                            <div class="rain-td th-hour1">{{ row.processing_object }}</div>
                            <div class="rain-td th-warning1">{{ row.contact_phone_new }}</div>
                            <div class="rain-td th-warning2" style="color: yellow">{{ row.push_status }}</div>
                          </div>
                        </div>
                      </div>
                      <div class="leftbei"></div>
                      <div class="righttbei"></div>
                      <div class="bottombeibg bottom-left"></div>
                    </div>
                    <div class="fanwei-right new-add-border">
                      <div class="neilao-small">移动泵车调度</div>
                      <div class="rain-monitor-table-container-neilao-right-b-r">
                        <div class="rain-monitor-table-header">
                          <!-- <div class="rain-th th-index1">单位名称</div> -->
                          <div class="rain-th th-station1">设施编号</div>
                          <div class="rain-th th-hour1">处理对象</div>
                          <div class="rain-th th-warning1">联系电话</div>
                          <div class="rain-th th-warning2">推送状态</div>
                        </div>
                        <div class="rain-monitor-table-body">
                          <div class="rain-tr" v-for="(row, idx) in yidongbengchediaodu" :key="row.index">
                            <!-- <div class="rain-td th-index1">{{ row.id }}</div> -->
                            <div class="rain-td th-station1">{{ row.facility_number }}</div>
                            <div class="rain-td th-hour1">{{ row.processing_object }}</div>
                            <div class="rain-td th-warning1">{{ row.contact_phone_new }}</div>
                            <div class="rain-td th-warning2" style="color: yellow">{{ row.push_status }}</div>
                          </div>
                        </div>
                      </div>
                      <div class="leftbei"></div>
                      <div class="righttbei"></div>
                      <div class="bottombeibg bottom-left"></div>
                    </div>
                  </div>
                </div>

                <!-- <div class="c-r-r-t-c">
                  <div class="c-r-r-t-ll" v-for="item in 3">
                    <div class="ll-img"></div>
                    <div class="ll-name">抽水机</div>
                    <div class="ll-value">11台</div>
                  </div>
                </div> -->
              </div>
            </div>
          </div>

          <!-- <div class="call-right-three"></div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineProps, watch } from 'vue'
import * as echarts from 'echarts'
import {
  valueyuliang,
  bjyaYyfaSelectData,
  getLocationsingleCallPolice,
  bjyaNlfsqkfx2,
  bjyaNlfsqkfx2Level,
  statisticsNEWGET,
  bjyaSbdbqkNEW,
  sbdbqkPhqNEW,
  bjyaSsddNEW,
  bjyaRyddNEW,
  bjyaYdbcddNEW,
  bjyaYdbcddGET,
  //
  caseInformations,
  nlxxtj,
  swGET,
  qsyltj,
  nlfsqkfx,
  qyhfbjsl,
  yjxxyzcdfj,
  bjyaYjlxtj,
  bjyaswcbsw,
  bjyafqdbqk,
  yxfwGET,
  bjyaYyfa,
  bjyaYxfw,
  bjyaYjlxtj2,
  statisticsListGET,
  paihongquGet,
  sbdbqkPhq,
  // sbdbqkHhchhsj,
  // sbdbqkHhcpsnl,
  sbdbqk
} from '@/api/callPolice'
import { cstqGET } from '@/api/intelligence'
import { getLocation } from '@/api/home'
// 获取assets静态资源
const getAssetsFile = url => {
  return new URL(`../../assets/images/home/<USER>
}
const emit = defineEmits(['tagClicked', 'yuntuClickShow', 'clickTable'])
// 时间和日期
const currentTime = ref('')
const currentDate = ref('')

let timer = null

// 更新时间函数
const updateTime = () => {
  const now = new Date()

  // 格式化时分秒
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 格式化年月日
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  currentDate.value = `${year} ${month} ${day}`
}

// 连接数字孪生配置
const options = ref({
  domId: 'player', // 修改为新的ID
  apiOptions: {
    onReady: function () {
      console.info('此时可以调API了')
    }
  }
})

const api = ref(null)
// 太原数字孪生内网地址
const host =
  ref(import.meta.env.VITE_DTS_URL) /
  // 组件挂载时启动定时器和初始化屏幕适配
  onMounted(async () => {
    updateTime() // 立即执行一次
    timer = setInterval(updateTime, 1000) // 每秒更新一次
    // 修改这里，接入地图配置
    // try {
    //   // 确保先引入了ac.min.js
    //   if (typeof acapi !== 'undefined') {
    //     // 创建数字孪生平台实例
    //     console.log('加载飞渡')
    //     api.value = new DigitalTwinPlayer(host.value, options.value)
    //     console.log('数字孪生平台初始化成功')
    //   } else {
    //     console.error('ac.min.js未正确加载，请检查引入路径')
    //   }
    // } catch (error) {
    //   console.error('数字孪生平台初始化失败:', error)
    // }
  })

// 组件卸载时清除定时器和屏幕适配事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }

  // 销毁DTS实例
  if (api.value) {
    api.value = null
  }
})
//
//
//
//

const bengzhanshuiwei = ref([])

const gwyjDATA = ref([
  {
    name: '总预警',
    value: 20
  },
  {
    name: '总预警',
    value: 20
  },
  {
    name: '总预警',
    value: 20
  }
])
// const valueyuliang = ref('')

const optionsYUliang = [
  {
    value: '40',
    label: '40'
  }
]
const fxzb = ref()
const ykxx = ref()
const bklxtj = ref()
const pieLeft = ref()
const pieRight = ref()
const callCharts = ref(null)

const nlxxtjData = ref()
const swDATA = ref()
const qsyltjDATA = ref({})
const nlfsqkfxDATA = ref({})
const sbdbqkDATA = ref({})
let pieTwoCharts = null
let pieOneCharts = null

const singleGetDataClick = item => {
  getLocationsingleCallPolice(item.name).then(res => {
    emit('clickScene', { tagData: {}, locationData: res.rows })
  })
}
const pieFunctioin = (dom, data, show) => {
  const pieData = ref({
    //   title: {
    //     text: 'Referer of a Website',
    //     subtext: 'Fake Data',
    //     left: 'center'
    //   },
    tooltip: {
      trigger: 'item',
      color: '#000',
      textStyle: { color: '#fff', fontSize: 22 },
      borderWidth: 0,
      axisPointer: { type: 'shadow' },
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      formatter: '{a} <br/>{b} : {d}%'
    },
    grid: {
      top: '10%',
      left: '22%',
      right: '10%',
      bottom: '-2%'
    },
    legend: {
      show,
      // orient: 'vertical',
      bottom: '4%',
      right: '25%',
      textStyle: {
        color: '#fff',
        fontSize: 24
      }
    },
    series: [
      {
        name: show ? '面积占比' : '风险占比',
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['50%', '40%'],
        data,
        label: {
          show: true,
          color: '#fff',
          fontSize: 23,
          formatter: function (params) {
            return params.data.value + '%'
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0
            // shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })

  if (show) {
    if (pieOneCharts) {
      pieOneCharts.dispose()
    }
    pieOneCharts = echarts.init(dom.value)
    pieOneCharts.setOption(pieData.value)
    pieOneCharts.on('click', function (params) {
      console.log(params, '就反丁')
      bjyaNlfsqkfx2Level(params.data.name).then(res => {
        console.log(res, '点击饼图得到的柱状图数据', JSON.parse(res.data[0].value))
        fxzbDOMchartsFunctioin(JSON.parse(res.data[0].value).flood_area_statistics_by_district)
      })
    })
  } else {
    if (pieTwoCharts) {
      pieTwoCharts.dispose()
    }
    pieTwoCharts = echarts.init(dom.value)
    pieTwoCharts.setOption(pieData.value)
  }
}

const qyhfbjslDATA = ref()

const bjyaswcbswDATA = ref()
const pashuineng = ref()
const bjyaYxfwDATa = ref({})
const bjyafqdbqkdata = ref()
const renyuandiaodu = ref([])
const cheliangdiaodu = ref([])
const yidongbengchediaodu = ref([])
const bjyaYjlxtj2Data = ref()
const yxfwDATA = ref()
const yuyanData = ref({})
const yuChange = value => {
  bjyaYyfa(value).then(res => {
    yuyanData.value = JSON.parse(res.data[0].value).data[0]
  })
  pageRefreshF()
}
let echartsingSW = null
const echartsingSWGET = swName => {
  swGET(swName).then(res => {
    console.log(res, '你你你')
    swDATA.value = JSON.parse(res.data[0].value).data
    console.log(swDATA.value, '内涝信息统计都图表')

    const optionss = {
      tooltip: {
        trigger: 'axis',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
        // formatter: '{a} <br/>{b} : {d}%'
      },
      grid: {
        left: '6%',
        right: '2%',
        top: '18%',
        bottom: '10%'
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          color: '#fff',
          fontSize: 22
        },
        axisLine: {
          lineStyle: {
            width: 0,
            color: '#A2A2A2'
          }
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        },
        data: swDATA.value.map(item => item.time)
      },
      legend: {
        data: ['积水深度'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        },
        itemStyle: {
          color: '#fff'
        },
        top: '4%'
      },
      title: {
        text: '水位 m',
        padding: [25, 30, 0, 23],
        textStyle: {
          color: '#fff',
          fontSize: 18
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 22
        },
        axisLine: {
          lineStyle: {
            width: 0,
            color: '#A2A2A2'
          }
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#A2A2A2'
          }
        }
      },
      series: [
        {
          data: swDATA.value.map(item => item.water_level),
          type: 'line',
          name: '积水深度',
          showSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          lineStyle: {
            color: '#58F9AE'
          },
          smooth: true
        },
        {
          data: new Array(swDATA.value.length).fill(0.15),
          type: 'line',
          name: '积水警戒线',
          showSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          lineStyle: {
            color: '#f8cc06ff'
          },
          smooth: true
        }
      ]
    }
    if (echartsingSW) {
      echartsingSW.dispose()
    }
    echartsingSW = echarts.init(callCharts.value)
    echartsingSW.setOption(optionss)
  })
}

const paihongquFlag = ref('1')
const bengzhanshuiweiHEADER = ref([])
let pashuinengCHARTS = null
const paishuiFunction = bjyaswcbswDATA => {
  const bjyaswcbswREF = {
    tooltip: {
      trigger: 'axis',
      color: '#000',
      textStyle: { color: '#fff', fontSize: 22 },
      borderWidth: 0,
      axisPointer: { type: 'shadow' },
      backgroundColor: 'rgba(0, 0, 0, 0.5)'
    },
    legend: {
      show: true,
      data: bjyaswcbswDATA.value2_name
        ? [bjyaswcbswDATA.value1_name, bjyaswcbswDATA.value2_name]
        : [bjyaswcbswDATA.value1_name],
      top: '3%',
      itemStyle: {
        // color: '#fff'
      },
      textStyle: {
        color: '#fff',
        fontSize: 20
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',

      axisLabel: {
        color: '#fff',
        fontSize: 22
      },
      axisLine: {
        lineStyle: {
          width: 0,
          color: '#A2A2A2'
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      data: bjyaswcbswDATA?.data.map(item => item.name)
    },
    yAxis: {
      type: 'value',
      name: bjyaswcbswDATA.unit,
      nameTextStyle: {
        color: '#fff',
        fontSize: 22,
        padding: [0, 34, 10, 0]
      },
      axisLabel: {
        color: '#fff',
        fontSize: 22
      },
      axisLine: {
        lineStyle: {
          width: 0,
          color: '#A2A2A2'
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: bjyaswcbswDATA.value2_name
      ? [
          {
            name: bjyaswcbswDATA.value1_name,
            type: 'line',
            data: bjyaswcbswDATA?.data.map(item => item.value1)
          },
          {
            name: bjyaswcbswDATA.value2_name,
            type: 'line',
            data: bjyaswcbswDATA?.data.map(item => item.value2)
          }
        ]
      : [
          {
            name: bjyaswcbswDATA.value1_name,
            type: 'line',
            data: bjyaswcbswDATA?.data.map(item => item.value1)
          }
        ]
  }
  if (pashuinengCHARTS) {
    pashuinengCHARTS.dispose()
  }
  pashuinengCHARTS = echarts.init(pashuineng.value)
  pashuinengCHARTS.setOption(bjyaswcbswREF)
}

const bjyaYxfwFunction = (level = '') => {
  bjyaYxfw(level).then(res => {
    if (level === '') {
      bjyaYxfwDATa.value = JSON.parse(res.data[0].value).data
    } else {
      bjyaYxfwDATa.value.details = JSON.parse(res.data[0].value).data
    }
    console.log(bjyaYxfwDATa.value, 'bjyaYxfwDATa.value影响范围')
  })
}
const yingxiangClick = item => {
  bjyaYxfwFunction(item)
}
const areaValue = ref('')
const gradeValue = ref('')
const typeValue = ref('')
const thirdQueryFunction = () => {
  statisticsListGET(areaValue.value, gradeValue.value, typeValue.value).then(res => {
    console.log(res, '三级联动查询')
    if (res.code === 200) {
      bjyaYjlxtj2Data.value = res.rows
    }
  })
}
const weaherData = ref({})
let zhuzhuangCharts = null
const fxzbDOMchartsFunctioin = zhuChartsData => {
  let barWidth = 54
  let myData1 = zhuChartsData.map(item => item.name)
  let syjcl = zhuChartsData.map(item => item.value)
  let dbyp = new Array(syjcl.length).fill(Math.max(...syjcl) * 1)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        textStyle: {
          color: '#fff'
        }
      },
      textStyle: {
        color: '#fff',
        fontSize: 24
      },
      backgroundColor: 'rgba(17,95,182,0.5)', //设置背景颜色
      borderColor: 'rgba(255, 255, 255, .8)',
      confine: true,
      formatter: '{b}: {c}'
    },
    // legend: {
    //   icon: 'rect',
    //   itemWidth: 14,
    //   itemHeight: 4,
    //   itemGap: 20,
    //   right: '14%',
    //   top: '4%',
    //   textStyle: {
    //     fontSize: '14px',
    //     color: '#EFF7FF'
    //   },
    //   data: ['食用菌产量'],
    //   selectedMode: false
    // },
    grid: {
      top: '12%',
      left: '4%',
      right: '4%',
      bottom: '2%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: myData1,
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        color: '#fff',
        interval: 0,
        fontSize: 24,
        align: 'center'
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#A2A2A2'
        }
      },
      splitLine: {
        type: 'dashed'
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        // name: '亿元',
        splitNumber: 4,
        type: 'value',
        nameTextStyle: {
          color: '#fff',
          fontSize: 20,
          align: 'center',
          padding: [0, 28, 4, 0]
        },
        axisLabel: {
          formatter: '{value}',
          textStyle: {
            fontSize: 22,
            color: '#fff',
            lineHeight: 16
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        //下半截柱状图
        name: '2020',
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        itemStyle: {
          //lenged文本
          opacity: 0.7,
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: '#3267AB' // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#67AFFC' // 100% 处的颜色
              }
            ],
            false
          )
        },
        label: {
          normal: {
            show: false,
            position: 'top',
            formatter: e => {
              // return e.value + '次';
              return e.value
            },
            fontSize: '14px',
            color: '#2EADFB',
            fontFamily: 'siyuan',
            fontWeight: 'bold',
            offset: [0, -5]
          }
        },
        data: syjcl
      },
      {
        // 替代柱状图 默认不显示颜色，是最下方柱图（邮件营销）的value值 - 20
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        stack: '广告',
        itemStyle: {
          color: 'transparent'
        },
        data: syjcl
      },
      {
        // 最大值，顶部圆片
        name: '',
        type: 'pictorialBar',
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, -barWidth / 4],
        z: 12,
        symbolPosition: 'end',
        itemStyle: {
          color: '#0B3F67',
          opacity: 0.2
        },
        data: dbyp
      },
      {
        // 中间圆片
        name: '',
        type: 'pictorialBar',
        symbolSize: [barWidth, barWidth / 2],
        symbolOffset: [0, -barWidth / 4],
        z: 12,
        itemStyle: {
          opacity: 1,
          color: '#2E8EDD'
        },
        symbolPosition: 'end',
        data: syjcl
      },
      {
        // 背景柱体
        name: '2019',
        type: 'bar',
        barWidth: barWidth,
        barGap: '-100%',
        z: 0,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 1,
                color: '#0B3F67' // 0% 处的颜色
              },
              {
                offset: 0,
                color: '#0B3F67' // 100% 处的颜色
              }
            ],
            false
          ),
          opacity: 0.2
        },
        data: dbyp
      }
    ]
  }
  if (zhuzhuangCharts) {
    zhuzhuangCharts.dispose()
  }
  zhuzhuangCharts = echarts.init(fxzb.value)
  zhuzhuangCharts.setOption(option)
}
const rainSelectDatas = ref([])
const pageRefreshF = () => {
  statisticsNEWGET().then(res => {
    console.log(res, '预报积水点信息')
    if (res.code === 200) {
      nlxxtjData.value = res.rows
    }
  })
  bjyaNlfsqkfx2().then(res => {
    console.log(res, '内涝发生情况分析', JSON.parse(res.data[0].value))
    pieFunctioin(pieLeft, JSON.parse(res.data[0].value).area_percentage_by_level, true)
    const zhuChartsData = JSON.parse(res.data[0].value).flood_area_statistics_by_district
    fxzbDOMchartsFunctioin(zhuChartsData)
  })
  bjyaSbdbqkNEW().then(res => {
    console.log(res, '排水能力不达标个数', JSON.parse(res.data[0].value))
    sbdbqkDATA.value = JSON.parse(res.data[0].value).data
  })
  sbdbqkPhqNEW('sbdbqk-bz').then(res => {
    console.log(res, '排水能力不达标个数下面的表格', JSON.parse(res.data[0].value).data)
    bengzhanshuiweiHEADER.value = JSON.parse(res.data[0].value).data.headers
    bengzhanshuiwei.value = JSON.parse(res.data[0].value).data.items
  })
  paihongquFlag.value = '1'
  bjyaSsddNEW().then(res => {
    console.log(res, '设施调度', JSON.parse(res.data[0].value).data)
    bjyafqdbqkdata.value = JSON.parse(res.data[0].value).data
  })
  bjyaYdbcddGET().then(res => {
    console.log(res, '移动泵车调度', JSON.parse(res.data[0].value).data)
    yidongbengchediaodu.value = JSON.parse(res.data[0].value).data
    yidongbengchediaodu.value.forEach(data => {
      data.contact_phone_new =
        data.contact_phone.slice(0, 3) + '****' + data.contact_phone.slice(data.contact_phone.length - 4)
    })
  })
  bjyaYdbcddNEW().then(res => {
    console.log(res, '车辆调度', JSON.parse(res.data[0].value).data)
    cheliangdiaodu.value = JSON.parse(res.data[0].value).data
    cheliangdiaodu.value.forEach(data => {
      data.contact_phone_new =
        data.contact_phone.slice(0, 3) + '****' + data.contact_phone.slice(data.contact_phone.length - 4)
    })
  })
  bjyaRyddNEW().then(res => {
    renyuandiaodu.value = JSON.parse(res.data[0].value).data
    renyuandiaodu.value.forEach(data => {
      data.contact_phone_new =
        data.contact_phone.slice(0, 3) + '****' + data.contact_phone.slice(data.contact_phone.length - 4)
    })
    console.log(res, '人员调度', renyuandiaodu.value)
  })
}
onMounted(async () => {
  const selectData = await bjyaYyfaSelectData()
  console.log(selectData, '降雨量列表', JSON.parse(selectData.data[0].value))
  rainSelectDatas.value = JSON.parse(selectData.data[0].value)
  if (rainSelectDatas.value.length) {
    valueyuliang.value = rainSelectDatas.value[0]
  }

  pageRefreshF()

  cstqGET().then(res => {
    console.log(res, '天气', JSON.parse(res.data[0].value))
    if (res.code === 200 && res.data.length) {
      weaherData.value = JSON.parse(res.data[0].value)
    }
  })

  bjyaYyfa(valueyuliang.value).then(res => {
    console.log(res, 'JSON.parse(res.data[0].value)雨中快报')
    yuyanData.value = JSON.parse(res.data[0].value).data[0]
  })
  // caseInformations().then(res => {
  // console.log(res)
  // })

  // 内涝信息统计
  // nlxxtj().then(res => {
  //   nlxxtjData.value = JSON.parse(res.data[0].value).data
  //   console.log(nlxxtjData, 'nlxxtjData')
  //   let swName = ''
  //   if (nlxxtjData.value.length) {
  //     swName = nlxxtjData.value[0].name
  //   }
  //   echartsingSWGET(swName)
  // })

  // qsyltj().then(res => {
  //   qsyltjDATA.value = JSON.parse(res.data[0].value).data
  // })

  nlfsqkfx().then(res => {
    nlfsqkfxDATA.value = JSON.parse(res.data[0].value).data
    console.log(JSON.parse(res.data[0].value).data, '各级面积风险')
    // pieFunctioin(pieLeft, nlfsqkfxDATA.value.risk_percentage_distribution_current, true)
    // pieFunctioin(pieRight, nlfsqkfxDATA.value.risk_percentage_distribution_hour, false)
  })

  qyhfbjsl().then(res => {
    // console.log(res, 'res', JSON.parse(res.data[0].value).data.regions)
    qyhfbjslDATA.value = JSON.parse(res.data[0].value).data.regions
    console.log(qyhfbjslDATA.value, '')

    const fengxian = {
      color: [
        '#63caff',
        '#49beff',
        '#03387a',
        '#03387a',
        '#03387a',
        '#6c93ee',
        '#a9abff',
        '#f7a23f',
        '#27bae7',
        '#ff6d9d',
        '#cb79ff',
        '#f95b5a',
        '#ccaf27',
        '#38b99c',
        '#93d0ff',
        '#bd74e0',
        '#fd77da',
        '#dea700'
      ],
      tooltip: {
        trigger: 'axis',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        formatter: function (params) {
          return (
            '<div>' +
            params[0].marker +
            '报警数量' +
            params[0].value +
            '</div>' +
            '<div>' +
            params[3].marker +
            '预测报警数量' +
            params[3].value +
            '</div>'
          )
        }
      },
      grid: {
        containLabel: true,
        left: 20,
        right: 20,
        bottom: 14,
        top: 30
      },
      xAxis: {
        axisLabel: {
          color: '#fff',
          fontSize: 26,
          interval: 0
        },
        axisTick: {
          lineStyle: {
            color: '#384267'
          },
          show: true
        },
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#A2A2A2',
            width: 1,
            type: 'dashed'
          },
          show: true
        },
        data: qyhfbjslDATA.value.map(item => item.name),
        type: 'category'
      },
      yAxis: {
        axisLabel: {
          color: '#fff',
          fontSize: 22
        },
        axisTick: {
          lineStyle: {
            color: '#A2A2A2',
            width: 1
          },
          show: true
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#A2A2A2',
            type: 'dashed'
          }
        },
        axisLine: {
          lineStyle: {
            color: '#A2A2A2',
            width: 1,
            type: 'dashed'
          },
          show: true
        },
        name: ''
      },
      series: [
        {
          data: qyhfbjslDATA.value.map(item => item.alarm_count),
          type: 'bar',
          barMaxWidth: 'auto',
          barWidth: 30,
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              type: 'linear',
              global: false,
              colorStops: [
                {
                  offset: 1,
                  color: '#2E8EDD'
                },
                {
                  offset: 0,
                  color: '#2E8Eaa'
                }
              ]
            }
          },
          label: {
            show: true,
            position: 'top',
            distance: 10,
            color: '#fff',
            fontSize: 20
          }
        },
        {
          data: [1, 1, 1, 1, 1, 1, 1, 1],
          type: 'pictorialBar',
          barMaxWidth: '20',
          symbol: 'diamond',
          symbolOffset: [0, '50%'],
          symbolSize: [30, 15],
          itemStyle: {
            color: '#2E8EDD'
          }
        },
        {
          data: qyhfbjslDATA.value.map(item => item.alarm_count),
          type: 'pictorialBar',
          barMaxWidth: '20',
          symbolPosition: 'end',
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [30, 12],
          zlevel: 2,
          itemStyle: {
            color: '#2E8EDD'
          }
        },
        {
          name: '',
          type: 'line',
          // yAxisIndex: 1,
          tooltip: {
            valueFormatter: function (value) {
              return value + ''
            }
          },
          data: qyhfbjslDATA.value.map(item => item.forecast_count)
        }
      ]
    }
    // const fxzbDOM = echarts.init(fxzb.value)
    // fxzbDOM.setOption(fengxian)
    // fxzbDOM.on('click', function (params) {
    //   console.log(params, '点击柱子')
    //   areaValue.value = areaValue.value === params.name ? '' : params.name
    //   thirdQueryFunction()
    // })
  })

  yjxxyzcdfj().then(res => {
    const fjdata = JSON.parse(res.data[0].value).data
    console.log(fjdata, '预警信息严重程度分级')

    let barWidth = 54

    const yujingxinxi = {
      tooltip: {
        trigger: 'axis',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        formatter: function (params) {
          return '<div>' + params[0].marker + params[0].name + params[0].value + '</div>'
        }
      },
      // legend: {
      //   icon: 'rect',
      //   itemWidth: 14,
      //   itemHeight: 4,
      //   itemGap: 20,
      //   right: '14%',
      //   top: '4%',
      //   textStyle: {
      //     fontSize: '14px',
      //     color: '#EFF7FF'
      //   },
      //   data: ['食用菌产量'],
      //   selectedMode: false
      // },
      grid: {
        top: '12%',
        left: '4%',
        right: '4%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: fjdata.map(item => item.level),
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#fff',
          interval: 0,
          fontSize: 22,
          align: 'center'
        },
        axisLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#A2A2A2'
          }
        },
        splitLine: {
          type: 'dashed'
        },
        axisTick: {
          show: false
        }
      },
      yAxis: [
        {
          type: 'value',
          // name: '亿元',
          splitNumber: 4,
          type: 'value',
          nameTextStyle: {
            color: '#fff',
            fontSize: 20,
            align: 'center',
            padding: [0, 28, 4, 0]
          },
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              fontSize: 22,
              color: '#fff',
              lineHeight: 16
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#A2A2A2',
              type: 'dashed'
            }
          }
        }
      ],
      series: [
        {
          //下半截柱状图
          name: '2020',
          type: 'bar',
          barWidth: barWidth,
          barGap: '-100%',
          itemStyle: {
            //lenged文本
            opacity: 0.7,
            color: function (params) {
              const colorData = ['#f30808ff', '#FFA500', '#ff0', '#2EADFB']
              return new echarts.graphic.LinearGradient(
                0,
                0,
                1,
                0,
                [
                  {
                    offset: 1,
                    color: colorData[params.dataIndex] // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: colorData[params.dataIndex] // 100% 处的颜色
                  }
                ],
                false
              )
            }
          },
          label: {
            normal: {
              show: false,
              position: 'top',
              formatter: e => {
                // return e.value + '次';
                return e.value
              },
              fontSize: '14px',
              color: '#2EADFB',
              fontFamily: 'siyuan',
              fontWeight: 'bold',
              offset: [0, -5]
            }
          },
          data: fjdata.map(item => item.value)
        },
        {
          // 替代柱状图 默认不显示颜色，是最下方柱图（邮件营销）的value值 - 20
          type: 'bar',
          barWidth: barWidth,
          barGap: '-100%',
          stack: '广告',
          itemStyle: {
            color: 'transparent'
          },
          data: new Array(fjdata.length).fill(Math.max(...fjdata.map(item => item.value)))
        },
        {
          // 最大值，顶部圆片
          name: '',
          type: 'pictorialBar',
          symbolSize: [barWidth, barWidth / 2],
          symbolOffset: [0, -barWidth / 4],
          z: 12,
          symbolPosition: 'end',
          itemStyle: {
            color: '#0B3F67',
            opacity: 0.2
          },
          data: fjdata.map(item => item.value)
        },
        {
          // 中间圆片
          name: '',
          type: 'pictorialBar',
          symbolSize: [barWidth, barWidth / 2],
          symbolOffset: [0, -barWidth / 4],
          z: 12,
          itemStyle: {
            //lenged文本
            opacity: 1,
            color: function (params) {
              const colorData = ['#f30808ff', '#FFA500', '#ff0', '#2EADFB']
              return new echarts.graphic.LinearGradient(
                0,
                0,
                1,
                0,
                [
                  {
                    offset: 1,
                    color: colorData[params.dataIndex] // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: colorData[params.dataIndex] // 100% 处的颜色
                  }
                ],
                false
              )
            }
          },
          symbolPosition: 'end',
          data: fjdata.map(item => item.value)
        },
        {
          // 背景柱体
          name: '2019',
          type: 'bar',
          barWidth: barWidth,
          barGap: '-100%',
          z: 0,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              1,
              0,
              [
                {
                  offset: 1,
                  color: '#0B3F67' // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: '#0B3F67' // 100% 处的颜色
                }
              ],
              false
            ),
            opacity: 0.2
          },
          data: new Array(fjdata.length).fill(Math.max(...fjdata.map(item => item.value)))
        }
      ]
    }
    const ykxxDOM = echarts.init(ykxx.value)
    ykxxDOM.setOption(yujingxinxi)
    ykxxDOM.on('click', function (params) {
      console.log(params, '点击大柱子')
      gradeValue.value = gradeValue.value === params.name ? '' : params.name
      thirdQueryFunction()
    })
  })

  bjyaYjlxtj().then(res => {
    console.log(res, 'res', JSON.parse(res.data[0].value).data)
    const xtjDATA = JSON.parse(res.data[0].value).data
    const bjtjData = {
      tooltip: {
        trigger: 'item',
        color: '#000',
        textStyle: { color: '#fff', fontSize: 22 },
        borderWidth: 0,
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      },
      series: [
        {
          name: '类型',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['35%', '66%'],
          data: xtjDATA,
          label: {
            color: '#fff',
            fontSize: 20,
            formatter: function (params) {
              return `${params.name} \n ${params.value}`
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    const bklxtjDOM = echarts.init(bklxtj.value)
    bklxtjDOM.setOption(bjtjData)
    bklxtjDOM.on('click', function (params) {
      console.log(params, '点击类型饼图')
      if (typeValue.value === params.name) {
        typeValue.value = ''
      } else {
        typeValue.value = params.name
      }
      thirdQueryFunction()
    })
  })

  // sbdbqk().then(res => {
  //   sbdbqkDATA.value = JSON.parse(res.data[0].value).data
  // })

  // bjyaswcbsw().then(res => {
  // console.log(res, '捷富凯');

  // bjyaswcbswDATA.value = JSON.parse(res.data[0].value).data
  // paishuiFunction(bjyaswcbswDATA.value)
  // })

  // bjyafqdbqk().then(res => {
  //   console.log(res, '总体概要设计', JSON.parse(res.data[0].value).data)
  //   bjyafqdbqkdata.value = JSON.parse(res.data[0].value).data
  // })
  // yxfwGET().then(res => {
  // console.log(res, 'res133333')
  // yxfwDATA.value = JSON.parse(res.data[0].value).data
  // })

  bjyaYjlxtj2().then(res => {
    // console.log(res, '22', JSON.parse(res.data[0].value).data)
    // bjyaYjlxtj2Data.value = JSON.parse(res.data[0].value).data
  })
  thirdQueryFunction()
  bjyaYxfwFunction()

  // sbdbqkPhq('sbdbqk-phq').then(res => {
  //   // bjyaYxfwDATa.value = JSON.parse(res.data[0].value).data
  //   console.log(JSON.parse(res.data[0].value).data, '排洪渠溢堤长度')
  //   bengzhanshuiwei.value = JSON.parse(res.data[0].value).data?.items
  //   bengzhanshuiweiHEADER.value = JSON.parse(res.data[0].value).data?.headers
  //   paihongquGet('zxt-phq').then(res => {
  //     // console.log(res, JSON.parse(res.data[0].value))
  //     paishuiFunction(JSON.parse(res.data[0].value))
  //   })
  // })
})

const singleClick = item => {
  console.log(item, 'item1')
  if (item.coordinate) {
    emit('clickTable', { tableData: { coordinate: item.coordinate, device_name: item.name } })
  }
}
const neilaoclick = async item => {
  console.log(item, 'item')
  item.sbname = item.name
  emit('clickScene', { tagData: { currentSingleData: true }, locationData: [item] })
  // if (item.coordinate) {
  // emit('clickTable', { tableData: { coordinate: item.coordinate, device_name: item.name } })
  // const coordinateArr = item.coordinate.split(',')
  // if (coordinateArr.length === 2) {
  //   const emitObj = {
  //     locationData: [
  //       {
  //         xcoordinate: coordinateArr[0],
  //         ycoordinate: coordinateArr[1]
  //       }
  //     ]
  //   }
  //   emit('tagClicked', emitObj)
  // }
  // }
  // echartsingSWGET(item.name)
}
const paihongquClick = item => {
  if (paihongquFlag.value === item) {
    return
  }
  paihongquFlag.value = item
  const paramsItem = ['', 'sbdbqk-bz', 'sbdbqk-phq', 'sbdbqk-hhcpsnl']
  sbdbqkPhq(paramsItem[item - 0]).then(res => {
    bengzhanshuiwei.value = JSON.parse(res.data[0].value).data?.items
    bengzhanshuiweiHEADER.value = JSON.parse(res.data[0].value).data?.headers
    console.log(JSON.parse(res.data[0].value).data, '排水能力不达标个数的点击事件')
  })

  // const paramsItemLine = ['', 'zxt-phq', 'zxt-hhcpsnl', 'zxt-hhchhsj', 'zxt-bz', 'zxt-jcj']
  // paihongquGet(paramsItemLine[item - 0]).then(res => {
  //   console.log(res, JSON.parse(res.data[0].value), '下面的echarts联动')
  //   paishuiFunction(JSON.parse(res.data[0].value))
  // })
}

const yuntuClick = () => {
  emit('yuntuClickShow', 1)
}
const yuzhongkuaibao = () => {
  emit('yuntuClickShow', 2)
}

const setMarkers = item => {
  if (item.coordinate) {
    // const coordinateArr = item.coordinate.split(',')
    // if (coordinateArr.length === 2) {
    //   const emitObj = {
    //     locationData: [
    //       {
    //         xcoordinate: coordinateArr[0],
    //         ycoordinate: coordinateArr[1],
    //         id: item.uuid
    //       }
    //     ]
    //   }
    //   console.log(emitObj, 'emitObj打点数据');

    //   emit('tagClicked', emitObj)
    // }
    emit('clickTable', { tableData: { coordinate: item.coordinate, device_name: '' } })
  }
}

const propsData = defineProps({
  shikeValue: {
    type: String
  }
})
watch(
  () => propsData.shikeValue,
  a => {
    console.log(a, '监听传递过来的值')
    nlfsqkfx(a).then(res => {
      nlfsqkfxDATA.value = JSON.parse(res.data[0].value).data
      pieFunctioin(pieLeft, nlfsqkfxDATA.value.risk_percentage_distribution_current, true)
      pieFunctioin(pieRight, nlfsqkfxDATA.value.risk_percentage_distribution_hour, false)
    })
  }
)
</script>

<style lang="scss" scoped>
.new-add-border {
  background: linear-gradient(155deg, rgba(0, 102, 255, 0.1) 0%, rgba(0, 102, 255, 0.1) 30%, rgba(0, 155, 255, 0) 100%);
  border: 2px solid rgba(64, 228, 251, 0.3);
  position: relative;
  .leftbei {
    position: absolute;
    left: -1px;
    width: 7px;
    height: 197px;
    top: 30%;
    background: url('@/assets/images/intellisense/leftbei.png') no-repeat center/100%;
  }
  .righttbei {
    position: absolute;
    right: -1px;
    width: 7px;
    height: 197px;
    top: 30%;
    background: url('@/assets/images/intellisense/rightbeibg.png') no-repeat center/100%;
  }
  .bottombeibg {
    position: absolute;
    bottom: -1px;
    width: 460px;
    height: 6px;
    left: 40%;
    background: url('@/assets/images/intellisense/bottombeibg.png') no-repeat center/100%;
    &.bottom-left {
      left: 23%;
    }
  }
}
.neilao-small {
  width: 342px;
  height: vh(50);
  background: url('@/assets/images/intellisense/neilaoxiao.png') no-repeat center/100%;
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 400;
  font-size: 27px;
  color: #ffffff;
  line-height: 33px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding-top: 8px;
  padding-left: 30px;
}
.title-more {
  font-family:
    Source Han Sans,
    Source Han Sans;
  font-weight: 400;
  font-size: 20px;
  color: #d8f1ff;
  line-height: 29px;
  text-align: right;
  font-style: normal;
  text-transform: none;
  padding-right: 10px;
}
.chart-headers {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: vh(45);
  margin-bottom: vh(5);
  background: url('@/assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  padding: 0;
  &.touchang {
    background: url('@/assets/images/intellisense/touchang.png') no-repeat;
  }
  &.touzhong {
    background: url('@/assets/images/intellisense/touzhong.png') no-repeat;
  }
  &.touduan {
    background: url('@/assets/images/intellisense/touduan.png') no-repeat;
  }
  .chart-title {
    font-weight: normal;
    font-size: vh(26);
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 vw(50);
    font-family: JiangChengXieHei;
    color: #d8f1ff;
    .title-value {
      color: #fff;
      font-family: Alibaba-PuHuiTi, sans-serif;
      font-size: 18px;
      font-weight: bold;
      padding-left: 30px;
      padding-top: 12px;
    }
    // @include respond-to("ultra-wide") {
    //   font-size: 18px;
    // }
  }

  .alarm-tabs {
    display: flex;
    height: 100%;
  }

  .alarm-tab {
    padding: 0 vw(20);
    height: 100%;
    display: flex;
    align-items: center;
    color: #fff;
    font-family: JiangChengXieHei, JiangChengXieHei;
    font-weight: normal;
    font-size: vh(20);
    cursor: pointer;
    margin-left: vw(5);
    background: url('@/assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;

    &.active {
      background: url('@/assets/images/home/<USER>') no-repeat;
      background-size: 100% 100%;
    }
  }
}
.call-wrap {
  height: calc(100% - vh(140));
  // display: flex;
  .call-left-wrap {
    display: flex;
    gap: 20px;
  }
  .call-left {
    width: 36%;
    padding-left: 20px;
    padding-top: 20px;
    // background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
    .call-yuyan {
      height: vh(488);
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      .yuyan-content {
        // display: flex;
        min-height: vh(270);
        gap: vh(20);
        justify-content: space-between;
        .yuyan-c-left {
          // width: 408px;
          padding-top: 27px;
          padding-left: 26px;
          .yuyan-c-l-input {
            display: flex;
            // justify-content: space-between;
            gap: 20px;
            align-items: center;
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 28px;
            color: #ffffff;
            line-height: 38px;
            text-align: center;
            font-style: normal;
            text-transform: none;
            :deep(.el-select__wrapper) {
              background: rgba(13, 72, 115, 0.6) !important;
              border: 1px solid #1f8ad4 !important;
              box-shadow: none;
            }
            :deep(.el-select__selected-item) {
              color: #fff;
              font-size: 24px;
            }
          }
          .yuyan-c-l-t {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 700;
            font-size: 28px;
            // color: red;
            color: #56abe9;
            line-height: 38px;
            font-style: normal;
            text-transform: none;
            padding: 30px 0 8px 0;
          }
          .yuyan-c-l-b {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 500;
            font-size: 28px;
            color: #ffffff;
            line-height: 38px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin: 12px 0;
          }
        }
        .yuyan-c-right {
          display: flex;
          gap: vh(10);
          margin-top: 40px;
          align-items: center;
          justify-content: space-around;

          .yuyan-c-r-item {
            position: relative;
            width: 164px;
            .yuyan-c-r-t {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 60px;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: 26px;
              color: #ffffff;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .yuyan-c-r-bottom {
              width: 100%;
              padding-top: 10px;
              height: vh(150);
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 900;
              font-size: 34px;
              color: #ffffff;
              line-height: 150px;
              text-align: center;
              font-style: normal;
              text-transform: none;
              &.yuyan-c-r-i0 {
                background: url('@/assets/images/intellisense/yuyanfangan1.png') no-repeat center/100%;
              }
              &.yuyan-c-r-i1 {
                background: url('@/assets/images/intellisense/yuyanfangan2.png') no-repeat center/100%;
              }
              &.yuyan-c-r-i2 {
                background: url('@/assets/images/intellisense/yuyanfangan3.png') no-repeat center/100%;
              }
              &.yuyan-c-r-i3 {
                background: url('@/assets/images/intellisense/yuyanfangan4.png') no-repeat center/100%;
              }
            }
          }
        }
      }
    }
    .call-neilao {
      // height: 70%;
      margin-top: 20px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);

      .call-charts {
        height: 420px;
      }
    }
  }
  .call-middle {
    width: 30%;
    .call-yuliang {
      height: 300px;
    }
    .guanwang-baojing {
      display: flex;
      .guanwang-wrap {
        flex: 1;
        color: #fff;
        font-size: 22px;
        .guanwang-item {
          height: 100px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        .guanwang-text {
          text-align: center;
        }
      }
    }
    .rain-monitor-table-container-middle {
      width: 100%;
      //   height: calc(100% - vh(45));

      border-radius: vh(8);
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .rain-monitor-table-header {
        display: flex;
        align-items: center;
        min-height: vh(48);
        background: #115a8f;
        color: #fff;
        font-weight: bold;
        font-size: vh(24);
        border-bottom: vh(2) solid #205080;
        font-family: Alibaba-PuHuiTi, sans-serif;
      }
      .rain-th,
      .rain-td {
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        // overflow: hidden;
        // white-space: nowrap;
        text-overflow: ellipsis;
      }
      .th-index1 {
        width: 33%;
      }
      .th-station1 {
        width: 12%;
      }
      .th-hour1 {
        width: 15%;
      }
      .th-warning1 {
        width: 13%;
      }
      .th-warning2 {
        width: 24%;
      }

      .rain-monitor-table-body {
        height: 230px;
        overflow-y: auto;
        -ms-overflow-style: none; /* IE 和 Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
      }
      .rain-monitor-table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      .rain-tr {
        display: flex;
        align-items: center;
        min-height: vh(44);
        font-size: vh(24);
        color: #fff;
        border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
        transition: background 0.2s;
        cursor: pointer;
      }
      .rain-tr:hover {
        background: rgba(0, 198, 255, 0.1);
      }
      .over-red {
        color: #ff3b3b;
        font-weight: bold;
      }
      .over-blue {
        color: #1a86fc;
        font-weight: bold;
      }
      .level-badge {
        display: inline-block;
        min-width: vw(28);
        padding: vh(2) vw(8);
        border-radius: vh(12);
        font-weight: bold;
        font-size: vh(18);
      }
      .level-red {
        color: #ff3b3b;
      }
      .level-blue {
        color: #1a86fc;
      }
      .level-light {
        color: #7ed6fc;
      }
    }
  }
  .call-right {
    width: 64%;
    // padding-left: 20px;
    padding-top: 20px;

    .c-r-i {
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
    }
    .n-l-f {
      // margin-top: 24px;
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.1556) 100%);
    }
    .call-neilao-mianji {
      display: flex;
      height: 324px;
      flex-wrap: wrap;
      gap: 30px;
      padding-left: 20px;
      .c-n-m-item {
        display: flex;
        align-items: center;
        width: 340px;
        justify-content: center;
        gap: 10px;
        .c-n-m-i-img {
          height: vh(126);
          width: vw(146);
          background: url('@/assets/images/intellisense/yuyanfangan.png') no-repeat center/100%;
        }
        .c-n-m-i-t {
          font-family:
            Source Han Sans,
            Source Han Sans;
          font-weight: 350;
          font-size: 26px;
          color: #ffffff;
          line-height: 29px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .c-n-m-i-v {
          font-family:
            Source Han Sans,
            Source Han Sans;
          font-weight: 900;
          font-size: 34px;
          color: #ffffff;
          line-height: 49px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .neilao-fasheng {
      padding: 0 0 0 20px;
      // height: vh(656);
    }

    .neilao-shishi {
      padding-top: 12px;
      .n-s-item {
        position: relative;
        width: 1030px;

        height: 60px;
        background: rgba(88, 249, 174, 0.1);
        border-radius: 0px 0px 0px 0px;
        border: 1px solid #58f9ae;
        margin: auto;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        &:nth-child(2) {
          background: rgba(86, 171, 233, 0.1);
          border-radius: 0px 0px 0px 0px;
          border: 1px solid #56abe9;
        }
        &:nth-child(3) {
          background: rgba(255, 242, 0, 0.1);
          border-radius: 0px 0px 0px 0px;
          border: 1px solid #fff200;
        }
        .n-s-i-left {
          width: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          .n-s-i-l-text {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 700;
            font-size: 22px;
            color: #ffffff;
            line-height: 32px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            margin-right: 12px;
          }
          .n-s-i-l-bold {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 900;
            font-size: 28px;
            line-height: 41px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
            background-clip: text;
            color: transparent;
          }
          .n-s-i-l-xiao {
            font-family:
              Source Han Sans,
              Source Han Sans;
            font-weight: 400;
            font-size: 22px;
            line-height: 41px;
            text-align: left;
            font-style: normal;
            text-transform: none;
            background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
            background-clip: text;
            color: transparent;
          }
          .n-s-i-l-img {
            margin-left: 10px;
            width: 20px;
            height: 40px;
            background: url('@/assets/images/intellisense/shang1.png') no-repeat center/140%;
          }
          .n-s-i-l-img-low {
            margin-left: 10px;
            width: 20px;
            height: 40px;
            background: url('@/assets/images/intellisense/shang1.png') no-repeat center/140%;
            transform: rotate(180deg);
          }
        }
        .n-s-i-line {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: auto;
          width: 1px;
          height: 32px;
          border: 1px solid #d8d8d8;
        }
      }
      .n-s-bottom {
        display: flex;
        align-items: center;
        width: 1030px;
        height: 40px;
        background: #0d4873;
        border: 1px solid;
        margin: auto;
        border-image: linear-gradient(
            90deg,
            rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
            rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
            rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
          )
          1 1;
        .n-s-b-t {
          flex: 1;
          font-family:
            Source Han Sans,
            Source Han Sans;
          font-weight: 500;
          font-size: 18px;
          color: #ffffff;
          line-height: 38px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
    .n-s-wrap-bottom {
      display: flex;
      padding-top: 10px;
      gap: 20px;
      .n-s-w-b-item {
        // flex: 1;

        .n-s-w-b-i-pie {
          height: 380px;
          // border: 1px solid red;
        }
        &:nth-child(1) {
          width: 40%;
        }
        &:nth-child(2) {
          width: 60%;
        }
      }
    }
    .rain-monitor-table-container-mianji {
      width: 100%;
      //   height: calc(100% - vh(45));

      border-radius: vh(8);
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .rain-monitor-table-header {
        display: flex;
        align-items: center;
        min-height: vh(48);
        background: #115a8f;
        color: #fff;
        font-weight: bold;
        font-size: vh(24);
        border-bottom: vh(2) solid #205080;
        font-family: Alibaba-PuHuiTi, sans-serif;
      }
      .rain-th,
      .rain-td {
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        // overflow: hidden;
        // white-space: nowrap;
        text-overflow: ellipsis;
      }
      .th-index1 {
        width: 33%;
      }
      .th-station1 {
        width: 12%;
      }
      .th-hour1 {
        width: 15%;
      }
      .th-warning1 {
        width: 13%;
      }
      .th-warning2 {
        width: 12%;
      }
      .th-warning3 {
        width: 12%;
      }

      .rain-monitor-table-body {
        height: 230px;
        overflow-y: auto;
        -ms-overflow-style: none; /* IE 和 Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
      }
      .rain-monitor-table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      .rain-tr {
        display: flex;
        align-items: center;
        min-height: vh(44);
        font-size: vh(24);
        color: #fff;
        border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
        transition: background 0.2s;
      }
      .rain-tr:hover {
        background: rgba(0, 198, 255, 0.1);
      }
      .over-red {
        color: #ff3b3b;
        font-weight: bold;
      }
      .over-blue {
        color: #1a86fc;
        font-weight: bold;
      }
      .level-badge {
        display: inline-block;
        min-width: vw(28);
        padding: vh(2) vw(8);
        border-radius: vh(12);
        font-weight: bold;
        font-size: vh(18);
      }
      .level-red {
        color: #ff3b3b;
      }
      .level-blue {
        color: #1a86fc;
      }
      .level-light {
        color: #7ed6fc;
      }
    }
    .call-right-pie {
      display: flex;
      div {
        flex: 1;
        height: 300px;
      }
    }
  }
  .call-left-w-bottom {
    // height: 400px;
    margin-top: 24px;
    .rain-monitor-table-container-neilao {
      width: 100%;
      //   height: calc(100% - vh(45));
      padding: 0 0;
      padding: 0 12px;
      display: flex;
      padding-top: 20px;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .rain-monitor-table-header {
        display: flex;
        align-items: center;
        min-height: vh(48);
        background: #115a8f;
        color: #fff;
        font-weight: bold;
        font-size: vh(26);
        border-bottom: vh(2) solid #205080;
        font-family: Alibaba-PuHuiTi, sans-serif;
        background: #0d4873;
        border: 1px solid;
        border-image: linear-gradient(
            90deg,
            rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
            rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
            rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
          )
          1 1;
      }
      .rain-th,
      .rain-td {
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        // overflow: hidden;
        // white-space: nowrap;
        text-overflow: ellipsis;
      }
      .th-index1 {
        width: 15%;
      }
      .th-station1 {
        width: 10%;
      }
      .th-hour1 {
        width: 10%;
      }
      .th-warning1 {
        width: 10%;
      }
      .th-warning2 {
        width: 10%;
      }
      .th-warning3 {
        width: 10%;
      }

      .rain-monitor-table-body {
        height: 500px;
        overflow-y: auto;
        -ms-overflow-style: none; /* IE 和 Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
      }
      .rain-monitor-table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari 和 Opera */
      }
      .rain-tr {
        cursor: pointer;
        display: flex;
        align-items: center;
        min-height: vh(54);
        color: #fff;
        border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
        transition: background 0.2s;
        font-family:
          Source Han Sans,
          Source Han Sans;
        font-weight: 350;
        font-size: vh(28);
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
        padding: 14px 0;
      }
      .rain-tr:hover {
        background: rgba(0, 198, 255, 0.1);
      }
      .over-red {
        color: #ff3b3b;
        font-weight: bold;
      }
      .over-blue {
        color: #1a86fc;
        font-weight: bold;
      }
      .level-badge {
        display: inline-block;
        min-width: vw(28);
        padding: vh(2) vw(8);
        border-radius: vh(12);
        font-weight: bold;
        font-size: vh(18);
      }
      .level-red {
        color: #ff3b3b;
      }
      .level-blue {
        color: #1a86fc;
      }
      .level-light {
        color: #7ed6fc;
      }
    }
  }
}
// 屏幕容器
.screen-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  pointer-events: none; // 设置为none，让鼠标事件穿透到地图
}

// 背景层
.bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-layer-1 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 1;
}

.bg-layer-2 {
  background-image: url('@/assets/images/home/<USER>');
  z-index: 2;
}

.bg-layer-3 {
  // background-image: url('@/assets/images/home/<USER>');
  z-index: 3;
}

// 地图容器样式
.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: auto;
}

// 拆分为三个独立的容器
.left-container,
.middle-container,
.right-container {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.left-container {
  left: 0;
  // width: 35%;
  // background: blue;
  .left-section {
    width: 100%;
    height: 100%;
    padding: vh(40) vw(0) vh(20) vw(100);
    pointer-events: auto;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .time-weather {
    display: flex;
    align-items: center;
    color: #fff;
    padding-left: vw(40);
    background: blue;
    .time-date {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .time {
      font-size: vh(24);
    }

    .date {
      font-size: vh(14);
      margin-right: vw(2);
    }

    .divider {
      margin: 0 vw(20);
      width: vw(1);
      height: vh(30);
      background-color: #3a607c;
    }

    .weather {
      display: flex;
      align-items: center;
    }

    .weather-icon {
      width: vw(24);
      height: vh(24);
      margin-right: vw(10);
    }

    .weather-icon1 {
      width: vw(24);
      height: vh(22);
    }

    .temperature {
      font-size: vh(24);
    }
  }

  .left-content {
    // background: rgba(255, 255, 255, 0.24);
    width: 2660px;
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
    margin-top: vh(10);
    gap: vh(20);

    .content-layout-first {
      flex: 1;
      width: 100%;
      // background: rgba(0, 0, 0, 0.2);
      .content-layout-header {
        width: 100%;
        height: vh(120);
        background-image: url('@/assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 vw(95);
        margin-bottom: 20px;
        .weather-info-bar {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          color: #fff;
          padding: 0 vw(15);
        }

        .current-weather {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 100%;
          gap: vw(48);
        }

        .weather-label {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          line-height: vh(33);
        }

        .city-info {
          display: flex;
          align-items: center;
        }

        .location-icon {
          display: inline-block;
          width: vw(22);
          height: vh(24);
          margin-right: vw(20);
        }

        .city-name {
          font-weight: normal;
          font-size: vh(32);
          color: #ffffff;
          margin-top: vh(-8);
        }

        .weather-detail {
          display: flex;
          align-items: flex-start;
          flex-direction: column;
          justify-content: center;
          height: 100%;
          gap: vw(15);
          min-width: 400px;
          font-size: 23px;
        }

        .weather-icon-large {
          display: flex;
          align-items: center;
        }

        .weather-icon-large img {
          width: vw(24);
          height: vh(24);
          margin-right: vw(10);
          object-fit: contain;
        }

        .weather-data {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .weather-type {
          display: flex;
          align-items: center;
          gap: vw(10);
        }

        .weather-name {
          font-weight: normal;
          font-size: vh(30);
          color: #ffffff;
          line-height: vh(42);
          text-align: center;
          font-style: normal;
        }

        .weather-temp {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          line-height: vh(30);
          font-style: normal;
          text-align: center;
          width: vw(72);
          height: vh(31);
          background: #1ecfa5;
          border-radius: vw(16);
        }

        .wind-info {
          display: flex;
          align-items: center;
          gap: vw(20);
          font-weight: normal;
          font-size: vh(27);
          color: #ffffff;
          text-align: left;
          font-style: normal;
          margin-top: vh(3);
        }

        .weather-forecast {
          display: flex;
          height: 100%;
          align-items: center;
        }

        .forecast-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 vw(30);
        }

        .forecast-title {
          font-weight: normal;
          font-size: vh(20);
          color: #ffffff;
          text-align: left;
          font-style: normal;
        }

        .forecast-value {
          .forecast-value-num {
            font-weight: bold;
            font-size: vh(33);
            color: #24ceb8;
            font-style: normal;
          }

          .forecast-value-unit {
            font-weight: normal;
            font-size: vh(24);
            color: #ffffff;
            font-style: normal;
          }
        }

        .forecast-value1 {
          .forecast-value-num {
            font-weight: bold;
            font-size: vh(24);
            color: #92d3ec;
            font-style: normal;
          }

          .forecast-value-unit {
            font-weight: normal;
            font-size: vh(24);
            color: #ffffff;
            font-style: normal;
          }
        }

        .publish-info {
          width: vw(225);
          height: vh(77);
          border-radius: vw(1);
          display: flex;
          justify-content: space-around;
          align-items: center;
          gap: vw(20);
        }

        .publish-label {
          display: flex;
          justify-content: center;
          align-items: center;
          // width: 40%;
          height: 100%;
          font-weight: normal;
          font-size: vh(25);
          padding: 0 12px;
          color: #ffffff;
          font-style: normal;
          background: rgba(110, 204, 255, 0.04);
          cursor: pointer;
        }

        .publish-times {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }

        .time-item {
          font-weight: normal;
          font-size: vh(24);
          color: #ffffff;
          font-style: normal;
        }

        .divider-line {
          width: vw(1);
          height: vh(70);
        }
      }
    }
  }
}

.middle-container {
  left: 46%;
  height: auto;
  .middle-section {
    /* 中间区域标题 */
    .section-title {
      font-family: YouSheBiaoTiHei;
      font-size: vh(80);
      color: #ffffff;
      text-align: center;
      font-style: normal;
    }

    /* 导航按钮 */
    .nav-buttons {
      display: flex;
      justify-content: center;
      gap: vw(20);
      margin-top: vh(60);
    }

    .nav-button {
      width: vw(200);
      height: vh(80);
      background-image: url('@/assets/images/home/<USER>');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      padding-top: vh(14);
      font-family: JiangChengXieHei;
      color: #fff;
      font-size: vh(24);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .nav-button.active {
      background-image: url('@/assets/images/home/<USER>');
      color: #fff;
      font-weight: bold;
    }
  }
}

.right-container {
  right: 0;
  // width: 35%;

  .right-section {
    width: 100%;
    height: 100%;
    padding: vh(40) vw(100) vh(20) vw(0);
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .user-portal-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: vh(10) vw(20);
    gap: vw(30);
    border-radius: vh(4);
    background: rgba(6, 72, 146, 0.24); // 添加背景色

    .user-info,
    .portal-back {
      display: flex;
      align-items: center;
      gap: vw(10);

      img {
        width: vh(40);
        height: vh(40);
        border-radius: 50%;
        object-fit: cover;
      }

      span {
        color: #fff;
        font-size: vh(16);
      }
    }
  }

  .right-content {
    // background: rgba(255, 255, 255, 0.1);
    border-radius: vh(4);
    width: 100%;
    // height: calc(100% - 50px);
    display: flex;
    gap: vw(20);
    > div {
      // flex: 1;
    }
    .call-right-one {
      // height: vh(1240);
      width: vw(710);
      background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
      .rain-monitor-table-container-tongji {
        width: 100%;
        //   height: calc(100% - vh(45));

        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        overflow: hidden;
        .rain-monitor-table-header {
          display: flex;
          align-items: center;
          min-height: vh(48);
          background: #115a8f;
          color: #fff;
          font-weight: bold;
          font-size: vh(24);
          border-bottom: vh(2) solid #205080;
          font-family: Alibaba-PuHuiTi, sans-serif;
          background: #0d4873;
          border: 1px solid;
          border-image: linear-gradient(
              90deg,
              rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
              rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
              rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
            )
            1 1;
        }
        .rain-th,
        .rain-td {
          text-align: center;
          padding: 0;
          margin: 0;
          box-sizing: border-box;
          // overflow: hidden;
          // white-space: nowrap;
          text-overflow: ellipsis;
        }
        .th-index1 {
          width: 25%;
        }
        .th-station1 {
          width: 12%;
        }
        .th-hour1 {
          width: 15%;
        }
        .th-warning1 {
          width: 17%;
        }
        .th-warning2 {
          width: 17%;
        }
        .th-warning3 {
          width: 12%;
        }

        .rain-monitor-table-body {
          height: 278px;
          overflow-y: auto;
          -ms-overflow-style: none; /* IE 和 Edge */
          scrollbar-width: none; /* Firefox */
          padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
        }
        .rain-monitor-table-body::-webkit-scrollbar {
          display: none; /* Chrome, Safari 和 Opera */
        }
        .rain-tr {
          display: flex;
          align-items: center;
          min-height: vh(44);
          font-size: vh(24);
          padding: 8px 0;
          color: #fff;
          font-family:
            Source Han Sans,
            Source Han Sans;
          border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
          transition: background 0.2s;
        }
        .rain-tr:hover {
          background: rgba(0, 198, 255, 0.1);
        }
        .over-red {
          color: #ff3b3b;
          font-weight: bold;
        }
        .over-blue {
          color: #1a86fc;
          font-weight: bold;
        }
        .level-badge {
          display: inline-block;
          min-width: vw(28);
          padding: vh(2) vw(8);
          border-radius: vh(12);
          font-weight: bold;
          font-size: vh(18);
        }
        .level-red {
          color: #ff3b3b;
        }
        .level-blue {
          color: #1a86fc;
        }
        .level-light {
          color: #7ed6fc;
        }
      }
    }
    .call-right-two {
      .c-r-t-top {
        display: flex;
        // height: vh(940);

        .c-r-t-t-l-right {
          // height: vh(850);
          width: 2380px;
          background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
          .c-r-t-t-l-r-wrap {
            display: flex;
            gap: 20px;
            .c-r-t-bottom {
              width: 40%;
              padding: 0 12px;
              background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
              // height: vh(300);
              .c-r-t-cc {
                display: flex;
                align-items: center;
                justify-content: space-around;
                margin: 18px 0 18px 0;
                padding-top: 34px;
                .c-r-t-cc-i {
                  cursor: pointer;
                  position: relative;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  width: 216px;
                  height: 50px;
                  background: url('@/assets/images/intellisense/tongji2.png') no-repeat center/100%;
                  &.cc-i-active {
                    background: url('@/assets/images/intellisense/tongji1.png') no-repeat center/100%;
                  }
                  .c-r-t-cc-t,
                  .c-r-t-cc-t-title {
                    font-family:
                      Source Han Sans,
                      Source Han Sans;
                    font-weight: 700;
                    font-size: 24px;
                    color: #ffffff;
                    line-height: 26px;
                    text-align: center;
                    font-style: normal;
                    text-transform: none;
                  }
                  .c-r-t-cc-t-title {
                    position: absolute;
                    top: -30px;
                    left: 80px;
                  }
                  .c-r-t-cc-bb {
                    position: relative;
                    bottom: 4px;
                    font-family:
                      Source Han Sans,
                      Source Han Sans;
                    font-weight: 900;
                    font-size: 26px;
                    line-height: 32px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    background: linear-gradient(90deg, #ffffff 0%, #e2b500 100%);
                    background-clip: text;
                    color: transparent;
                  }
                }
              }
              .rain-monitor-table-container-tongji1 {
                width: 100%;
                //   height: calc(100% - vh(45));

                border-radius: vh(8);
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                overflow: hidden;
                .rain-monitor-table-header {
                  display: flex;
                  align-items: center;
                  min-height: vh(48);
                  background: #115a8f;
                  color: #fff;
                  font-weight: bold;
                  font-size: vh(27);
                  border-bottom: vh(2) solid #205080;
                  font-family: Alibaba-PuHuiTi, sans-serif;
                  background: #0d4873;
                  border: 1px solid;
                  border-image: linear-gradient(
                      90deg,
                      rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                      rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                      rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                    )
                    1 1;
                }
                .rain-th,
                .rain-td {
                  text-align: center;
                  padding: 0;
                  margin: 0;
                  box-sizing: border-box;
                  // overflow: hidden;
                  // white-space: nowrap;
                  text-overflow: ellipsis;
                }
                .th-index1 {
                  width: 33%;
                }
                .th-station1 {
                  width: 33%;
                }
                .th-hour1 {
                  width: 34%;
                }
                .th-warning1 {
                  width: 13%;
                }
                .th-warning2 {
                  width: 12%;
                }
                .th-warning3 {
                  width: 12%;
                }

                .rain-monitor-table-body {
                  height: 344px;
                  overflow-y: auto;
                  -ms-overflow-style: none; /* IE 和 Edge */
                  scrollbar-width: none; /* Firefox */
                  padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
                }
                .rain-monitor-table-body::-webkit-scrollbar {
                  display: none; /* Chrome, Safari 和 Opera */
                }
                .rain-tr {
                  padding: 12px 0;
                  display: flex;
                  align-items: center;
                  min-height: vh(44);
                  font-size: vh(28);
                  color: #fff;
                  border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
                  transition: background 0.2s;
                  cursor: pointer;
                }
                .rain-tr:hover {
                  background: rgba(0, 198, 255, 0.1);
                }
                .over-red {
                  color: #ff3b3b;
                  font-weight: bold;
                }
                .over-blue {
                  color: #1a86fc;
                  font-weight: bold;
                }
                .level-badge {
                  display: inline-block;
                  min-width: vw(28);
                  padding: vh(2) vw(8);
                  border-radius: vh(12);
                  font-weight: bold;
                  font-size: vh(18);
                }
                .level-red {
                  color: #ff3b3b;
                }
                .level-blue {
                  color: #1a86fc;
                }
                .level-light {
                  color: #7ed6fc;
                }
              }
            }
            .c-r-t-t-l-r-w-right {
              width: 60%;
              background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);
              .rain-monitor-table-container-neilao-r-wrap {
                width: 100%;
                //   height: calc(100% - vh(45));

                display: flex;
                padding-top: 14px;
                flex-direction: column;
                box-sizing: border-box;
                padding-left: 20px;
                overflow: hidden;
                .rain-monitor-table-header {
                  display: flex;
                  align-items: center;
                  min-height: vh(48);
                  background: #115a8f;
                  color: #fff;
                  font-weight: bold;
                  font-size: vh(27);
                  border-bottom: vh(2) solid #205080;
                  font-family: Alibaba-PuHuiTi, sans-serif;
                  background: #0d4873;
                  border: 1px solid;
                  border-image: linear-gradient(
                      90deg,
                      rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                      rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                      rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                    )
                    1 1;
                }
                .rain-th,
                .rain-td {
                  text-align: center;
                  padding: 0;
                  margin: 0;
                  box-sizing: border-box;
                  // overflow: hidden;
                  // white-space: nowrap;
                  text-overflow: ellipsis;
                }

                .th-station1 {
                  width: 25%;
                }
                .th-hour1 {
                  width: 30%;
                }
                .th-warning1 {
                  width: 23%;
                }
                .th-warning2 {
                  width: 22%;
                  padding: 0 4px;
                }

                .rain-monitor-table-body {
                  height: vh(400);
                  overflow-y: auto;
                  -ms-overflow-style: none; /* IE 和 Edge */
                  scrollbar-width: none; /* Firefox */
                  padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
                }
                .rain-monitor-table-body::-webkit-scrollbar {
                  display: none; /* Chrome, Safari 和 Opera */
                }
                .rain-tr {
                  display: flex;
                  align-items: center;
                  min-height: vh(50);
                  color: #fff;
                  border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
                  transition: background 0.2s;
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 350;
                  font-size: vh(28);
                  color: #ffffff;
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                  padding: 12px 0;
                }
                .rain-tr:hover {
                  background: rgba(0, 198, 255, 0.1);
                }
                .over-red {
                  color: #ff3b3b;
                  font-weight: bold;
                }
                .over-blue {
                  color: #1a86fc;
                  font-weight: bold;
                }
                .level-badge {
                  display: inline-block;
                  min-width: vw(28);
                  padding: vh(2) vw(8);
                  border-radius: vh(12);
                  font-weight: bold;
                  font-size: vh(18);
                }
                .level-red {
                  color: #ff3b3b;
                }
                .level-blue {
                  color: #1a86fc;
                }
                .level-light {
                  color: #7ed6fc;
                }
              }
            }
          }
          .c-r-right-top {
            display: flex;
            justify-content: center;
            margin-top: 12px;
            gap: 10px;
            .c-r-t-item {
              width: 200px;
              height: 180px;
              background: rgba(27, 105, 190, 0.24);
              .c-r-t-i-bottom {
                .c-r-t-i-b-d {
                  margin-top: 14px;
                  position: relative;
                  font-family:
                    Source Han Sans,
                    Source Han Sans;
                  font-weight: 700;
                  font-size: 35px;
                  color: #ffffff;
                  line-height: 82px;
                  text-align: center;
                  font-style: normal;
                  text-transform: none;
                  .b-d-name {
                    position: absolute;
                    bottom: -30px;
                    right: 20px;
                    font-family:
                      Source Han Sans,
                      Source Han Sans;
                    font-weight: 350;
                    font-size: 23px;
                    color: #ffffff;
                    line-height: 33px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                  }
                }
              }
            }
          }
          .paishuinengli {
            height: 325px;
          }
          .yingxiangfanwei {
            margin-top: 22px;
            background: linear-gradient(270deg, rgba(6, 72, 146, 0.2353) 0%, rgba(6, 79, 156, 0.0556) 100%);

            .y-x-f-f-w-w {
              display: flex;
              gap: 80px;
            }
            .fanwei-middle {
              width: 35%;
            }
            .fanwei-right {
              width: 30%;
              .fanwei-ying {
                display: flex;
                margin-top: 16px;
                padding-left: 12px;
                gap: 14px;
                .f-y-item {
                  // display: flex;
                  width: 300px;
                  height: 180px;
                  background: rgba(27, 105, 190, 0.24);
                  .f-y-i-w {
                    display: flex;
                    justify-content: space-around;
                  }
                  .f-y-b {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: relative;
                    width: 82px;
                    height: 82px;
                    background: url('@/assets/images/intellisense/yingxiang1.png') no-repeat center/100%;
                    font-family:
                      Source Han Sans,
                      Source Han Sans;
                    font-weight: 700;
                    font-size: 35px;
                    color: #ffffff;
                    line-height: 51px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                    &.zhong {
                      background: url('@/assets/images/intellisense/yingxiang2.png') no-repeat center/100%;
                    }
                    &.zhongdu {
                      background: url('@/assets/images/intellisense/yingxiang3.png') no-repeat center/100%;
                    }
                    .f-y-b-b {
                      position: absolute;
                      bottom: -30px;
                      right: 17px;
                      font-family:
                        Source Han Sans,
                        Source Han Sans;
                      font-weight: 350;
                      font-size: 23px;
                      color: #ffffff;
                      line-height: 33px;
                      text-align: left;
                      font-style: normal;
                      text-transform: none;
                    }
                  }
                }
              }
            }
            .fanwei-left {
              width: 35%;
            }
          }
          .rain-monitor-table-container-neilao-right {
            width: 100%;
            //   height: calc(100% - vh(45));

            display: flex;
            padding-top: 14px;
            flex-direction: column;
            box-sizing: border-box;
            padding-left: 20px;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              min-height: vh(48);
              background: #115a8f;
              color: #fff;
              font-weight: bold;
              font-size: vh(27);
              border-bottom: vh(2) solid #205080;
              font-family: Alibaba-PuHuiTi, sans-serif;
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              // overflow: hidden;
              // white-space: nowrap;
              text-overflow: ellipsis;
            }

            .th-station1 {
              width: 20%;
              padding: 0 12px;
            }
            .th-hour1 {
              width: 20%;
              padding: 0 12px;
            }
            .th-warning1 {
              width: 25%;
              padding: 0 12px;
            }
            .th-warning2 {
              width: 20%;
              // padding: 0 4px;
            }
            .th-warning3 {
              width: 15%;
              // padding: 0 4px;
            }

            .rain-monitor-table-body {
              height: vh(580);
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              padding: 14px 0;
              display: flex;
              align-items: center;
              min-height: vh(50);
              color: #fff;
              border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
              transition: background 0.2s;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: vh(28);
              color: #ffffff;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(18);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }

          .c-r-r-t-c {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            .c-r-r-t-ll {
              display: flex;
              align-items: center;
              .ll-img {
                width: 48px;
                height: 48px;
                background: url('@/assets/images/intellisense/fenxi1.png') no-repeat center/100%;
              }
              .ll-name {
                margin: 0 8px;
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 350;
                font-size: 22px;
                color: #949fbc;
                line-height: 32px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
              .ll-value {
                font-family:
                  Source Han Sans,
                  Source Han Sans;
                font-weight: 700;
                font-size: 24px;
                color: #ffffff;
                line-height: 35px;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
            }
          }
          .rain-monitor-table-container-neilao-right-b {
            width: 100%;
            //   height: calc(100% - vh(45));

            display: flex;
            padding-top: 14px;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              min-height: vh(48);
              background: #115a8f;
              color: #fff;
              font-weight: bold;
              font-size: vh(27);
              border-bottom: vh(2) solid #205080;
              font-family: Alibaba-PuHuiTi, sans-serif;
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              padding: 0;
              margin: 0;
              box-sizing: border-box;
              // overflow: hidden;
              // white-space: nowrap;
              text-overflow: ellipsis;
            }
            .th-index1 {
              width: 22%;
              padding: 0 12px;
            }
            .th-station1 {
              width: 15%;
            }
            .th-hour1 {
              width: 25%;
              padding: 0 12px;
            }
            .th-warning1 {
              width: 23%;
            }
            .th-warning2 {
              width: 15%;
            }

            .rain-monitor-table-body {
              height: 580px;
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              padding: 14px 0;
              display: flex;
              align-items: center;
              min-height: vh(50);
              color: #fff;
              border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
              transition: background 0.2s;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: vh(28);
              color: #ffffff;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(18);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }
          .rain-monitor-table-container-neilao-right-b-r {
            width: 100%;
            //   height: calc(100% - vh(45));

            display: flex;
            padding-top: 14px;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
            .rain-monitor-table-header {
              display: flex;
              align-items: center;
              min-height: vh(48);
              background: #115a8f;
              color: #fff;
              font-weight: bold;
              font-size: vh(27);
              border-bottom: vh(2) solid #205080;
              font-family: Alibaba-PuHuiTi, sans-serif;
              background: #0d4873;
              border: 1px solid;
              border-image: linear-gradient(
                  90deg,
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0),
                  rgba(133.00000727176666, 208.0000028014183, 216.00000232458115, 1),
                  rgba(13.000000175088644, 72.00000330805779, 115.00000074505806, 0)
                )
                1 1;
            }
            .rain-th,
            .rain-td {
              text-align: center;
              margin: 0;
              box-sizing: border-box;
              // overflow: hidden;
              // white-space: nowrap;
              text-overflow: ellipsis;
            }

            .th-station1 {
              width: 25%;
            }
            .th-hour1 {
              width: 30%;
              padding: 0 10px;
            }
            .th-warning1 {
              width: 23%;
            }
            .th-warning2 {
              width: 22%;
            }

            .rain-monitor-table-body {
              height: 580px;
              overflow-y: auto;
              -ms-overflow-style: none; /* IE 和 Edge */
              scrollbar-width: none; /* Firefox */
              padding-bottom: vh(10); /* 添加底部填充确保完全显示 */
            }
            .rain-monitor-table-body::-webkit-scrollbar {
              display: none; /* Chrome, Safari 和 Opera */
            }
            .rain-tr {
              padding: 14px 0;
              display: flex;
              align-items: center;
              min-height: vh(50);
              color: #fff;
              border-bottom: vh(2) solid rgba(223, 236, 240, 0.1);
              transition: background 0.2s;
              font-family:
                Source Han Sans,
                Source Han Sans;
              font-weight: 350;
              font-size: vh(28);
              color: #ffffff;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
            .rain-tr:hover {
              background: rgba(0, 198, 255, 0.1);
            }
            .over-red {
              color: #ff3b3b;
              font-weight: bold;
            }
            .over-blue {
              color: #1a86fc;
              font-weight: bold;
            }
            .level-badge {
              display: inline-block;
              min-width: vw(28);
              padding: vh(2) vw(8);
              border-radius: vh(12);
              font-weight: bold;
              font-size: vh(18);
            }
            .level-red {
              color: #ff3b3b;
            }
            .level-blue {
              color: #1a86fc;
            }
            .level-light {
              color: #7ed6fc;
            }
          }
        }
      }
    }
    .fengxian-zhanbi {
      height: 260px;
    }
    .yujingxinxi-fenji {
      height: 260px;
    }
    .baojing-tongji {
      height: 260px;
    }
  }
}
</style>

<style lang="scss">
.call-selectp {
  .el-select-dropdown__item {
    font-size: 28px;
    padding: 12px 0;
    height: 54px;
    padding-left: 12px;
  }
}
</style>
