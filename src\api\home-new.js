import request from '../utils/request'

export function getYltj() {
  return request({
    url: '/taiyuan/zhzs-yltj/stat',
    method: 'get'
  })
}

export function getSjtj() {
  return request({
    url: '/taiyuan/zhzs-sjtj/stat',
    method: 'get'
  })
}

export function getBzyx() {
  return request({
    url: '/taiyuan/zhzs-psssnlbz/stat',
    method: 'get'
  })
}

export function getGw() {
  return request({
    url: '/taiyuan/zhzs-psssnlgw/stat',
    method: 'get'
  })
}

export function getRywz() {
  return request({
    url: '/taiyuan/zhzs-rywztj/stat',
    method: 'get'
  })
}

export function getDdzltj() {
  return request({
    url: '/taiyuan/zhzs-ddzltj/stat',
    method: 'get'
  })
}

export function getDdzl() {
  return request({
    url: '/taiyuan/zhzs-ddzl/stat',
    method: 'get'
  })
}

export function getYldbj() {
  return request({
    url: '/taiyuan/zhzs-yldbj/stat',
    method: 'get'
  })
}

export function getYldbjtb() {
  return request({
    url: '/taiyuan/statistics/list?type=低洼路段',
    method: 'get'
  })
}

export function getPsss() {
  return request({
    url: '/taiyuan/zhzs-psssbj/stat',
    method: 'get'
  })
}

export function getPssstb() {
  return request({
    url: '/taiyuan/statistics/list?type=雨水管网',
    method: 'get'
  })
}

export function gettbRight(params) {
  let url = `/taiyuan/statistics/list?pageNum=${params.pageNum}&pageSize=${params.pageSize}`

  // 添加查询条件
  if (params.area) {
    url += `&area=${encodeURIComponent(params.area)}`
  }
  if (params.type) {
    url += `&type=${encodeURIComponent(params.type)}`
  }

  return request({
    url,
    method: 'get'
  })
}
export function getddzltb() {
  return request({
    url: '/taiyuan/zhzs-ddzl/stat',
    method: 'get'
  })
}

export function getsghx(params) {
  return request({
    url: '/taiyuan/zhzs-qytj/stat',
    method: 'get'
  })
}

export function getbcLocation() {
  return request({
    url: '/taiyuan/fkclxxb/list?cllx=泵车',
    method: 'get'
  })
}
export function getwzLocation() {
  return request({
    url: '/taiyuan/wz/list?pageSize=10000',
    method: 'get'
  })
}

// 韦恩图联动
// echarts地图数据
export function getMapData(name) {
  return request({
    url: `/taiyuan/zhzs-qytj2/stat?name=${name}`,
    method: 'get'
  })
}

// 柱状图数据
export function getBarData(name) {
  return request({
    url: `/taiyuan/zhzs-sjtj2/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-区域概览
export function getRlqygl(name) {
  return request({
    url: `/taiyuan/zhzs-rl-qytj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-韦恩图联动-传参方式name=1-2025-07-03(name前面取值为1-7)在后面拼接日期
export function getRlvet(name) {
  return request({
    url: `/taiyuan/zhzs-rl-qytj2/stat?name=${name}`,
    method: 'get'
  })
}


// 降雨日历-雨量统计-左边第一个柱状图-从上往下数
export function getRlyltj(name) {
  return request({
    url: `/taiyuan/zhzs-rl-yltj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-事件统计-左边第二个柱状图-从上往下数
export function getRlsjtj(name) {
  return request({
    url: `/taiyuan/zhzs-rl-sjtj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-泵站运行统计
export function getRlbzyxtj(name) {
  return request({
    url: `/taiyuan/zhzs-rl-psssnlbz/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-管网总长
export function getRlgwzc(name) {
  return request({
    url: `/taiyuan/zhzs-rl-psssnlgw/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-人员物资统计
export function getRlrywz(name) {
  return request({
    url: `/taiyuan/zhzs-rl-rywztj/stat?name=${name}`,
    method: 'get'
  })
}

// 降雨日历-调度指令统计
export function getRlddzltj(name) {
  return request({
    url: `/taiyuan/zhzs-rl-ddzltj/stat?name=${name}`,
    method: 'get'
  })
}
// 降雨日历-调度指令
export function getRlddzl(name) {
  return request({
    url: `/taiyuan/zhzs-rl-ddzl/stat?name=${name}`,
    method: 'get'
  })
}